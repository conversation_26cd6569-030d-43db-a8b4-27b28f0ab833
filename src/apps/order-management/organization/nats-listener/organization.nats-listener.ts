import { CoordinatesCommandBuilder } from '@wisemen/coordinates'
import { IntegrationEvent } from '../../../../modules/integration-events/integration-event.js'
import { NatsSubscriber } from '../../../../modules/nats/nats-application/subscribers/nats-subscriber.decorator.js'
import { NatsMessageData } from '../../../../modules/nats/nats-application/parameters/nats-message-data.decorator.js'
import { NatsMsgDataJsonPipe } from '../../../../modules/nats/nats-application/parameters/pipes/nats-message-data-json.pipe.js'
import { IntegrationEventType } from '../../../../modules/integration-events/integration-event.type.js'
import { WiseCrmNatsClient } from '../../../../modules/nats/nats-application/clients/nats-crm.client.js'
import { CreateOrganizationUseCase } from '../use-cases/create-organization/create-organization.use-case.js'
import { UpdateOrganizationUseCase } from '../use-cases/update-organization/update-organization.use-case.js'
import { CreateOrganizationCommandBuilder } from '../use-cases/create-organization/create-organization.command.builder.js'
import { UpdateOrganizationCommandBuilder } from '../use-cases/update-organization/update-organization.command.builder.js'
import { natsSubject } from '../../../../modules/nats/nats-application/nats-subject.js'
import { Address } from '../../../../utils/address/address.js'
import { OnNatsMessage } from '../../../../modules/nats/nats-application/message-handler/on-nats-message.decorator.js'
import { AddressCommandBuilder } from '../../../../utils/address/address-command.builder.js'
import { OrganizationCreatedIntegrationEventData } from './organization-created-data.js'
import { OrganizationUpdatedIntegrationEventData } from './organization-updated-data.js'

const ORGANIZATION_REQUEST_SUBJECT = '{env}.wise-crm.tenant.*.business.>'

@NatsSubscriber(configService => ({
  client: WiseCrmNatsClient,
  subject: natsSubject(ORGANIZATION_REQUEST_SUBJECT, {
    env: configService.getOrThrow<string>('NODE_ENV')
  })
}))
export class OrganizationNatsSubscriber {
  constructor (
    private readonly createOrganizationUseCase: CreateOrganizationUseCase,
    private readonly updateOrganizationUseCase: UpdateOrganizationUseCase
  ) {}

  @OnNatsMessage()
  handleEvent (
    @NatsMessageData(NatsMsgDataJsonPipe) event: IntegrationEvent
  ): Promise<void> | void {
    switch (event.type) {
      case IntegrationEventType.BUSINESS_CREATED: return this.handleBusinessCreatedEvent(event)
      case IntegrationEventType.BUSINESS_UPDATED: return this.handleBusinessUpdatedEvent(event)
      default: throw new Error(`Unsupported type: ${event.type}`)
    }
  }

  async handleBusinessCreatedEvent (event: IntegrationEvent): Promise<void> {
    const data = event.data as OrganizationCreatedIntegrationEventData

    const address = JSON.parse(data.address) as Address
    const addressCommand = new AddressCommandBuilder()
      .withStreetName(address.streetName)
      .withStreetNumber(address.streetNumber)
      .withUnit(address.unit)
      .withCity(address.city)
      .withPostalCode(address.postalCode)
      .withCountry(address.country)
      .withCoordinates(new CoordinatesCommandBuilder()
        .withLatitude(address.coordinates?.latitude ?? 0)
        .withLongitude(address.coordinates?.longitude ?? 0)
        .build())
      .build()

    const createOrganizationCommand = new CreateOrganizationCommandBuilder()
      .withName(data.name)
      .withCrmBusinessUuid(data.businessUuid)
      .withVatNumber(data.vatNumber)
      .withAddress(addressCommand)
      .build()

    await this.createOrganizationUseCase.execute(createOrganizationCommand)
  }

  async handleBusinessUpdatedEvent (event: IntegrationEvent): Promise<void> {
    const data = event.data as OrganizationUpdatedIntegrationEventData

    const address = JSON.parse(data.address) as Address
    const addressCommand = new AddressCommandBuilder()
      .withStreetName(address.streetName)
      .withStreetNumber(address.streetNumber)
      .withCity(address.city)
      .withPostalCode(address.postalCode)
      .withCountry(address.country)
      .withUnit(address.unit)
      .withCoordinates(new CoordinatesCommandBuilder()
        .withLatitude(address.coordinates?.latitude ?? 0)
        .withLongitude(address.coordinates?.longitude ?? 0)
        .build())
      .build()

    const updateOrganizationCommand = new UpdateOrganizationCommandBuilder()
      .withName(data.name)
      .withCrmBusinessUuid(data.businessUuid)
      .withVatNumber(data.vatNumber)
      .withAddress(addressCommand)
      .build()

    await this.updateOrganizationUseCase.execute(data.businessUuid, updateOrganizationCommand)
  }
}
