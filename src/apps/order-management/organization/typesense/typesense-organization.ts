import { Organization } from '../entities/organization.entity.js'

export class TypesenseOrganization {
  id: string
  externalId?: string
  crmBusinessUuid?: string
  name: string
  vatNumber?: string
  country: string | undefined
  city: string | undefined
  postalCode: string | undefined
  streetName: string | undefined
  streetNumber: string | undefined
  unit: string | undefined
  coordinates: [number, number] | undefined
  abbreviation?: string

  constructor (organization: Organization) {
    this.id = organization.uuid
    this.externalId = organization.externalId ?? undefined
    this.crmBusinessUuid = organization.crmBusinessUuid ?? undefined
    this.name = organization.name
    this.abbreviation = organization.abbreviation ?? undefined
    this.vatNumber = organization.vatNumber ?? undefined
    this.country = organization.address.country ?? undefined
    this.city = organization.address.city ?? undefined
    this.postalCode = organization.address.postalCode ?? undefined
    this.streetName = organization.address.streetName ?? undefined
    this.streetNumber = organization.address.streetNumber ?? undefined
    this.unit = organization.address.unit ?? undefined
    const coordinates = organization.address.coordinates
    this.coordinates = coordinates
      ? [coordinates.latitude, coordinates.longitude]
      : undefined// Default to [0, 0] if no coordinates are provided
  }
}
