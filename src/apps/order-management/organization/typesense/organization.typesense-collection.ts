import { TypesenseCollectionName } from '../../../../modules/typesense/collections/typesense-collection-name.enum.js'
import { RegisterTypesenseCollection } from '../../../../modules/typesense/collections/typesense-collection.decorator.js'
import { TypesenseCollection } from '../../../../modules/typesense/collections/typesense.collection.js'

@RegisterTypesenseCollection(TypesenseCollectionName.ORGANIZATION)
export class OrganizationTypesenseCollection extends TypesenseCollection {
  readonly name = TypesenseCollectionName.ORGANIZATION

  readonly searchableFields = [
    { name: 'name', type: 'string', sort: true, infix: true },
    { name: 'externalId', type: 'string', infix: true, sort: true },
    { name: 'crmBusinessUuid', type: 'string', sort: true, infix: true, optional: true },
    { name: 'vatNumber', type: 'string', sort: true, infix: true, optional: true },
    { name: 'country', type: 'string', sort: true, infix: true, optional: true },
    { name: 'city', type: 'string', sort: true, infix: true, optional: true },
    { name: 'postalCode', type: 'string', sort: true, infix: true, optional: true },
    { name: 'streetName', type: 'string', sort: true, infix: true, optional: true },
    { name: 'streetNumber', type: 'string', sort: true, infix: true, optional: true },
    { name: 'unit', type: 'string', sort: true, infix: true, optional: true }
  ] as const

  readonly filterableFields = [
    { name: 'abbreviation', type: 'string', optional: true },
    { name: 'coordinates', type: 'geopoint', optional: true }
  ] as const

  readonly referenceFields = [] as const
}
