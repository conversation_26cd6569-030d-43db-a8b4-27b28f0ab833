import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Not, Repository } from 'typeorm'
import { Organization } from '../../entities/organization.entity.js'
import { UpdateOrganizationCommand } from './update-organization.command.js'

export class UpdateOrganizationRepository {
  constructor (
    @InjectRepository(Organization) private readonly organizationRepo: Repository<Organization>
  ) {}

  async getOrganizationWithCrmBusinessUuid (crmBusinessUuid: string): Promise<Organization | null> {
    return await this.organizationRepo.findOneBy({ crmBusinessUuid: crmBusinessUuid })
  }

  async otherOrganizationExistsWithExternalId (
    externalId: string,
    organizationUuid: string
  ): Promise<boolean> {
    return await this.organizationRepo.existsBy({
      externalId,
      uuid: Not(organizationUuid)
    })
  }

  async update (organizationUuid: string, command: UpdateOrganizationCommand): Promise<void> {
    await this.organizationRepo.update(
      { uuid: organizationUuid },
      {
        externalId: command.externalId,
        name: command.name,
        abbreviation: command.abbreviation,
        crmBusinessUuid: command.crmBusinessUuid,
        vatNumber: command.vatNumber,
        address: command.address.parse()
      }
    )
  }
}
