import { ApiProperty } from '@nestjs/swagger'
import { Coordinates } from '@wisemen/coordinates'
import { PaginatedOffsetResponse } from '../../../../../utils/pagination/offset/paginated-offset.response.js'
import { TypesenseOrganization } from '../../typesense/typesense-organization.js'
import { AddressResponse } from '../../../../../utils/address/address-response.js'
import { AddressBuilder } from '../../../../../utils/address/address.builder.js'

export class GetOrganizationsResponseItem {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  @ApiProperty({ type: String, nullable: true })
  externalId: string | null

  @ApiProperty({ type: String, format: 'uuid', nullable: true })
  crmBusinessUuid: string | null

  @ApiProperty({ type: String, nullable: true })
  vatNumber: string | null

  @ApiProperty({ type: AddressResponse })
  address: AddressResponse

  constructor (organization: TypesenseOrganization) {
    this.uuid = organization.id
    this.name = organization.name
    this.externalId = organization.externalId ?? null
    this.crmBusinessUuid = organization.crmBusinessUuid ?? null
    this.vatNumber = organization.vatNumber ?? null
    this.address = new AddressResponse(
      new AddressBuilder()
        .withCity(organization.city)
        .withCountry(organization.country)
        .withPostalCode(organization.postalCode)
        .withStreetName(organization.streetName)
        .withStreetNumber(organization.streetNumber)
        .withUnit(organization.unit)
        .withCoordinates(
          organization.coordinates
            ? new Coordinates(organization.coordinates[0], organization.coordinates[1])
            : null
        )
        .build()
    )
  }
}

export class GetOrganizationsResponse
  extends PaginatedOffsetResponse<GetOrganizationsResponseItem> {
  @ApiProperty({ type: GetOrganizationsResponseItem, isArray: true })
  declare items: GetOrganizationsResponseItem[]

  constructor (organizations: PaginatedOffsetResponse<TypesenseOrganization>) {
    const items = organizations.items.map(
      organization => new GetOrganizationsResponseItem(organization)
    )
    super(items, organizations.meta)
  }
}
