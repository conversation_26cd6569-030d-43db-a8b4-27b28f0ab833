import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { Organization } from '../../entities/organization.entity.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { OrganizationEntityBuilder } from '../../builders/organization.entity.builder.js'
import { CreateOrganizationCommand } from './create-organization.command.js'
import { CreateOrganizationResponse } from './create-organization.response.js'
import { OrganizationExternalIdAlreadyExistsError } from './organization-external-id-already-exists.error.js'
import { OrganizationCreatedEvent } from './organization-created.event.js'

@Injectable()
export class CreateOrganizationUseCase {
  constructor (
    private readonly datasource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(Organization)
    private readonly organizationRepo: Repository<Organization>
  ) {}

  async execute (command: CreateOrganizationCommand): Promise<CreateOrganizationResponse> {
    if (command.externalId != null
      && await this.organizationRepo.existsBy({ externalId: command.externalId })) {
      throw new OrganizationExternalIdAlreadyExistsError(command.externalId)
    }

    const organization = new OrganizationEntityBuilder()
      .withExternalId(command.externalId)
      .withName(command.name)
      .withAbbreviation(command.abbreviation)
      .withCrmBusinessUuid(command.crmBusinessUuid)
      .withVatNumber(command.vatNumber)
      .withAddress(command.address.parse())
      .build()

    await transaction(this.datasource, async () => {
      await this.organizationRepo.insert(organization)
      await this.eventEmitter.emitOne(new OrganizationCreatedEvent(organization))
    })

    return new CreateOrganizationResponse(organization)
  }
}
