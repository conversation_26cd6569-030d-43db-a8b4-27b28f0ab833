import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm'
import { Address } from '../../../../utils/address/address.js'
import { AddressColumn } from '../../../../utils/address/address-column.js'

@Entity()
export class Organization {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @Index()
  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @Column({ type: 'varchar', unique: true, nullable: true })
  externalId: string | null

  @Index()
  @Column({ type: 'uuid', unique: true, nullable: true })
  crmBusinessUuid: string | null

  @Column({ type: 'varchar' })
  name: string

  @Column({ type: 'varchar', nullable: true })
  abbreviation: string | null

  @Column({ type: 'varchar', nullable: true })
  vatNumber: string | null

  @AddressColumn()
  address: Address

  @Column({ type: 'varchar', nullable: true })
  remarksForPlanner: string | null

  @Column({ type: 'varchar', nullable: true })
  remarksForDriver: string | null
}
