import { ApiProperty } from '@nestjs/swagger'
import { IsEmail, IsEnum, IsNumber, IsObject, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { Language, LanguageApiProperty } from '../../../utils/language/language.enum.js'
import { AddressCommand } from '../../../utils/address/address-command.js'

export class InvoiceDetailCommand {
  @ApiProperty({ type: String, format: 'email' })
  @IsEmail()
  email: string

  @ApiProperty({ type: Number })
  @IsNumber()
  discountPerKm: number

  @ApiProperty({ type: Number })
  @IsNumber()
  discountPerMin: number

  @LanguageApiProperty()
  @IsEnum(Language)
  language: Language

  @ApiProperty({ type: AddressCommand })
  @IsObject()
  @Type(() => AddressCommand)
  @ValidateNested()
  address: AddressCommand
}
