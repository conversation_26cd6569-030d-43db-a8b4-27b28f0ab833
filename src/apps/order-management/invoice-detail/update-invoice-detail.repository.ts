import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { InvoiceDetailCommand } from './invoice-detail.command.js'
import { InvoiceDetail } from './invoice-detail.entity.js'

export class UpdateInvoiceDetailRepository {
  constructor (
    @InjectRepository(InvoiceDetail) private readonly invoiceDetailRepo: Repository<InvoiceDetail>
  ) {}

  async update (invoiceDetailUuid: string, command: InvoiceDetailCommand): Promise<void> {
    await this.invoiceDetailRepo.update(
      { uuid: invoiceDetailUuid },
      {
        email: command.email,
        discountPerKm: command.discountPerKm,
        discountPerMin: command.discountPerMin,
        language: command.language,
        address: command.address.parse()
      })
  }
}
