import { AddressCommandBuilder } from '../../../utils/address/address-command.builder.js'
import { AddressCommand } from '../../../utils/address/address-command.js'
import { Language } from '../../../utils/language/language.enum.js'
import { InvoiceDetailCommand } from './invoice-detail.command.js'

export class InvoiceDetailCommandBuilder {
  private command: InvoiceDetailCommand

  constructor () {
    this.command = new InvoiceDetailCommand()
    this.command.email = '<EMAIL>'
    this.command.discountPerKm = 0
    this.command.discountPerMin = 0
    this.command.language = Language.NL
    this.command.address = new AddressCommandBuilder().build()
  }

  withEmail (email: string): this {
    this.command.email = email
    return this
  }

  withDiscountPerKm (discountPerKm: number): this {
    this.command.discountPerKm = discountPerKm
    return this
  }

  withDiscountPerMin (discountPerMin: number): this {
    this.command.discountPerMin = discountPerMin
    return this
  }

  withLanguage (language: Language): this {
    this.command.language = language
    return this
  }

  withAddress (address: AddressCommand): this {
    this.command.address = address
    return this
  }

  build (): InvoiceDetailCommand {
    return this.command
  }
}
