import { ApiProperty } from '@nestjs/swagger'
import { Language, LanguageApiProperty } from '../../../utils/language/language.enum.js'
import { AddressResponse } from '../../../utils/address/address-response.js'
import { InvoiceDetail } from './invoice-detail.entity.js'

export class InvoiceDetailResponse {
  @ApiProperty({ type: String })
  email: string

  @ApiProperty({ type: Number })
  discountPerKm: number

  @ApiProperty({ type: Number })
  discountPerMin: number

  @LanguageApiProperty()
  language: Language

  @ApiProperty({ type: AddressResponse })
  address: AddressResponse

  constructor (invoiceDetail: InvoiceDetail) {
    this.email = invoiceDetail.email
    this.discountPerKm = invoiceDetail.discountPerKm
    this.discountPerMin = invoiceDetail.discountPerMin
    this.language = invoiceDetail.language
    this.address = new AddressResponse(invoiceDetail.address)
  }
}
