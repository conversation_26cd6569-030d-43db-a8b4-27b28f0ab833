import { randomUUID } from 'crypto'
import { Language } from '../../../utils/language/language.enum.js'
import { AddressBuilder } from '../../../utils/address/address.builder.js'
import { Address } from '../../../utils/address/address.js'
import { InvoiceDetail } from './invoice-detail.entity.js'

export class InvoiceDetailEntityBuilder {
  private readonly invoiceDetail: InvoiceDetail

  constructor () {
    this.invoiceDetail = new InvoiceDetail()
    this.invoiceDetail.uuid = randomUUID()
    this.invoiceDetail.email = 'email'
    this.invoiceDetail.discountPerKm = 0
    this.invoiceDetail.discountPerMin = 0
    this.invoiceDetail.language = Language.NL
    this.invoiceDetail.address = new AddressBuilder().build()
  }

  withUuid (uuid: string): this {
    this.invoiceDetail.uuid = uuid
    return this
  }

  withEmail (email: string): this {
    this.invoiceDetail.email = email
    return this
  }

  withDiscountPerKm (discountPerKm: number): this {
    this.invoiceDetail.discountPerKm = discountPerKm
    return this
  }

  withDiscountPerMin (discountPerMin: number): this {
    this.invoiceDetail.discountPerMin = discountPerMin
    return this
  }

  withLanguage (language: Language): this {
    this.invoiceDetail.language = language
    return this
  }

  withAddress (address: Address): this {
    this.invoiceDetail.address = address
    return this
  }

  build (): InvoiceDetail {
    return this.invoiceDetail
  }
}
