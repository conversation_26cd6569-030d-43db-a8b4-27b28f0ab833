import { <PERSON>umn, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'
import { Language } from '../../../utils/language/language.enum.js'
import { AddressColumn } from '../../../utils/address/address-column.js'
import { Address } from '../../../utils/address/address.js'

@Entity()
export class InvoiceDetail {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @Column({ type: 'varchar' })
  email: string

  @Column('int4', { default: 0 })
  discountPerKm: number

  @Column('int4', { default: 0 })
  discountPerMin: number

  @Column({ type: 'enum', enum: Language, default: Language.NL })
  language: Language

  @AddressColumn()
  address: Address
}
