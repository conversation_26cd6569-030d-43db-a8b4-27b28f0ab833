import { CareUser } from '../entities/care-user.entity.js'

export class TypesenseCareUser {
  id: string
  name: string
  email: string | undefined
  phone: string | undefined
  mobilePhone: string | undefined
  socialSecurityNumber: string | undefined
  externalId: string | undefined
  crmIndividualUuid: string | null
  country: string | undefined
  city: string | undefined
  postalCode: string | undefined
  streetName: string | undefined
  streetNumber: string | undefined
  unit: string | undefined
  coordinates: [number, number] | undefined

  constructor (careUser: CareUser) {
    this.id = careUser.uuid
    this.name = careUser.name
    this.externalId = careUser.externalId ?? undefined
    this.crmIndividualUuid = careUser.crmIndividualUuid
    this.email = careUser.email ?? undefined
    this.phone = careUser.phone ?? undefined
    this.mobilePhone = careUser.mobilePhone ?? undefined
    this.socialSecurityNumber = careUser.socialSecurityNumber ?? undefined
    this.country = careUser.address?.country ?? undefined
    this.city = careUser.address?.city ?? undefined
    this.postalCode = careUser.address?.postalCode ?? undefined
    this.streetName = careUser.address?.streetName ?? undefined
    this.streetNumber = careUser.address?.streetNumber ?? undefined
    this.unit = careUser.address?.unit ?? undefined
    const coordinates = careUser.address?.coordinates
    this.coordinates = coordinates
      ? [coordinates.latitude, coordinates.longitude]
      : undefined
  }
}
