import { CoordinatesCommandBuilder } from '@wisemen/coordinates'
import { IntegrationEvent } from '../../../../modules/integration-events/integration-event.js'
import { NatsSubscriber } from '../../../../modules/nats/nats-application/subscribers/nats-subscriber.decorator.js'
import { NatsMessageData } from '../../../../modules/nats/nats-application/parameters/nats-message-data.decorator.js'
import { NatsMsgDataJsonPipe } from '../../../../modules/nats/nats-application/parameters/pipes/nats-message-data-json.pipe.js'
import { IntegrationEventType } from '../../../../modules/integration-events/integration-event.type.js'
import { CreateCareUserCommandBuilder } from '../use-cases/create-care-user/create-care-user.command.builder.js'
import { CreateCareUserUseCase } from '../use-cases/create-care-user/create-care-user.use-case.js'
import { UpdateCareUserUseCase } from '../use-cases/update-care-user/update-care-user.use-case.js'
import { UpdateCareUserCommandBuilder } from '../use-cases/update-care-user/update-care-user.command.builder.js'
import { WiseCrmNatsClient } from '../../../../modules/nats/nats-application/clients/nats-crm.client.js'
import { natsSubject } from '../../../../modules/nats/nats-application/nats-subject.js'
import { Address } from '../../../../utils/address/address.js'
import { OnNatsMessage } from '../../../../modules/nats/nats-application/message-handler/on-nats-message.decorator.js'
import { AddressCommandBuilder } from '../../../../utils/address/address-command.builder.js'
import { AddressCommand } from '../../../../utils/address/address-command.js'
import { CareUserCreatedIntegrationEventData } from './care-user-created-data.js'
import { CareUserUpdatedIntegrationEventData } from './care-user-updated-data.js'

const CARE_USER_REQUEST_SUBJECT = '{env}.wise-crm.tenant.*.individual.>'

@NatsSubscriber(configService => ({
  client: WiseCrmNatsClient,
  subject: natsSubject(CARE_USER_REQUEST_SUBJECT, {
    env: configService.getOrThrow<string>('NODE_ENV')
  })
}))
export class CareUserNatsSubscriber {
  constructor (
    private readonly createCareUserUseCase: CreateCareUserUseCase,
    private readonly updateCareUserUseCase: UpdateCareUserUseCase
  ) {}

  @OnNatsMessage()
  handleEvent (
    @NatsMessageData(NatsMsgDataJsonPipe) event: IntegrationEvent
  ): Promise<void> | void {
    switch (event.type) {
      case IntegrationEventType.INDIVIDUAL_CREATED: return this.handleIndividualCreatedEvent(event)
      case IntegrationEventType.INDIVIDUAL_UPDATED: return this.handleIndividualUpdatedEvent(event)
      default: throw new Error(`Unsupported type: ${event.type}`)
    }
  }

  async handleIndividualCreatedEvent (event: IntegrationEvent): Promise<void> {
    const data = event.data as CareUserCreatedIntegrationEventData

    let addressCommand: AddressCommand | null = null

    if (data.address !== null) {
      const address = JSON.parse(data.address) as Address
      addressCommand = new AddressCommandBuilder()
        .withStreetName(address.streetName)
        .withStreetNumber(address.streetNumber)
        .withUnit(address.unit)
        .withCity(address.city)
        .withPostalCode(address.postalCode)
        .withCountry(address.country)
        .withCoordinates(new CoordinatesCommandBuilder()
          .withLatitude(address.coordinates?.latitude ?? 0)
          .withLongitude(address.coordinates?.longitude ?? 0)
          .build())
        .build()
    }

    const createCareUserCommand = new CreateCareUserCommandBuilder()
      .withName(data.firstName + ' ' + data.lastName)
      .withCrmIndividualUuid(data.individualUuid)
      .withEmail(data.email)
      .withPhone(data.phone)
      .withMobilePhone(data.mobilePhone)
      .withSocialSecurityNumber(data.socialSecurityNumber)
      .withAddress(addressCommand)
      .build()

    await this.createCareUserUseCase.execute(createCareUserCommand)
  }

  async handleIndividualUpdatedEvent (event: IntegrationEvent): Promise<void> {
    const data = event.data as CareUserUpdatedIntegrationEventData

    let addressCommand: AddressCommand | null = null

    if (data.address !== null) {
      const address = JSON.parse(data.address) as Address
      addressCommand = new AddressCommandBuilder()
        .withStreetName(address.streetName)
        .withStreetNumber(address.streetNumber)
        .withUnit(address.unit)
        .withCity(address.city)
        .withPostalCode(address.postalCode)
        .withCountry(address.country)
        .withCoordinates(new CoordinatesCommandBuilder()
          .withLatitude(address.coordinates?.latitude ?? 0)
          .withLongitude(address.coordinates?.longitude ?? 0)
          .build())
        .build()
    }

    const updateCareUserCommand = new UpdateCareUserCommandBuilder()
      .withName(data.firstName + ' ' + data.lastName)
      .withCrmIndividualUuid(data.individualUuid)
      .withEmail(data.email)
      .withPhone(data.phone)
      .withMobilePhone(data.mobilePhone)
      .withSocialSecurityNumber(data.socialSecurityNumber)
      .withAddress(addressCommand)
      .build()

    await this.updateCareUserUseCase.execute(data.individualUuid, updateCareUserCommand)
  }
}
