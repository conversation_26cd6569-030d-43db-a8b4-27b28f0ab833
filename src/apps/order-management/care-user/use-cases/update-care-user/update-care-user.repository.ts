import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { CareUser } from '../../entities/care-user.entity.js'
import { UpdateCareUserCommand } from './update-care-user.command.js'

export class UpdateCareUserRepository {
  constructor (
    @InjectRepository(CareUser) private readonly careUserRepo: Repository<CareUser>
  ) {}

  async getCareUserWithCrmIndividualUuid (crmIndividualUuid: string): Promise<CareUser | null> {
    return await this.careUserRepo.findOneBy({ crmIndividualUuid: crmIndividualUuid })
  }

  async update (
    careUserUuid: string,
    command: UpdateCareUserCommand
  ): Promise<void> {
    await this.careUserRepo.update(
      { uuid: careUserUuid },
      {
        externalId: command.externalId,
        name: command.name,
        crmIndividualUuid: command.crmIndividualUuid,
        email: command.email,
        phone: command.phone,
        mobilePhone: command.mobilePhone,
        socialSecurityNumber: command.socialSecurityNumber,
        address: command.address?.parse()
      }
    )
  }
}
