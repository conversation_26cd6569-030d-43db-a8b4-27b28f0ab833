import { ApiProperty } from '@nestjs/swagger'
import { Coordinates } from '@wisemen/coordinates'
import {
  PaginatedOffsetResponse
} from '../../../../../utils/pagination/offset/paginated-offset.response.js'
import { TypesenseCareUser } from '../../typesense/typesense-care-user.js'
import { AddressResponse } from '../../../../../utils/address/address-response.js'
import { AddressBuilder } from '../../../../../utils/address/address.builder.js'

export class GetCareUsersResponseItem {
  @ApiProperty({ format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, nullable: true })
  externalId: string | null

  @ApiProperty({ type: String, format: 'uuid', nullable: true })
  crmIndividualUuid: string | null

  @ApiProperty()
  name: string

  @ApiProperty({ type: String, nullable: true })
  email: string | null

  @ApiProperty({ type: String, nullable: true })
  phone: string | null

  @ApiProperty({ type: String, nullable: true })
  mobilePhone: string | null

  @ApiProperty({ type: String, nullable: true })
  socialSecurityNumber: string | null

  @ApiProperty({ type: AddressResponse, nullable: true })
  address: AddressResponse | null

  @ApiProperty({ type: 'integer' })
  executedOrders: number

  @ApiProperty({ type: 'integer' })
  plannedOrders: number

  constructor (careUser: TypesenseCareUser) {
    this.uuid = careUser.id
    this.name = careUser.name
    this.externalId = careUser.externalId != null ? careUser.externalId : null
    this.crmIndividualUuid = careUser.crmIndividualUuid
    this.email = careUser.email ?? null
    this.phone = careUser.phone ?? null
    this.mobilePhone = careUser.mobilePhone ?? null
    this.socialSecurityNumber = careUser.socialSecurityNumber ?? null
    this.address = new AddressResponse(
      new AddressBuilder()
        .withCity(careUser.city)
        .withCountry(careUser.country)
        .withPostalCode(careUser.postalCode)
        .withStreetName(careUser.streetName)
        .withStreetNumber(careUser.streetNumber)
        .withUnit(careUser.unit)
        .withCoordinates(
          careUser.coordinates
            ? new Coordinates(careUser.coordinates[0], careUser.coordinates[1])
            : null
        )
        .build()
    )
    this.executedOrders = 0
    this.plannedOrders = 0
  }
}

export class GetCareUsersResponse extends PaginatedOffsetResponse<GetCareUsersResponseItem> {
  @ApiProperty({ type: GetCareUsersResponseItem, isArray: true })
  declare items: GetCareUsersResponseItem[]

  constructor (careUsers: PaginatedOffsetResponse<TypesenseCareUser>) {
    const items = careUsers.items.map(careUser => new GetCareUsersResponseItem(careUser))
    super(items, careUsers.meta)
  }
}
