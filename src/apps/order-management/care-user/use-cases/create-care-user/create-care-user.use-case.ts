import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { CareUser } from '../../entities/care-user.entity.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { CreateCareUserResponse } from './create-care-user.response.js'
import { CreateCareUserCommand } from './create-care-user.command.js'
import { CareUserCreatedEvent } from './care-user-created.event.js'
import { CareUserExternalIdAlreadyExistsError } from './care-user-external-id-already-exists.error.js'

@Injectable()
export class CreateCareUserUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(CareUser) private readonly careUserRepository: Repository<CareUser>
  ) {}

  async execute (command: CreateCareUserCommand): Promise<CreateCareUserResponse> {
    if (await this.careUserRepository.existsBy({ externalId: command.externalId })) {
      throw new CareUserExternalIdAlreadyExistsError(command.externalId)
    }

    const careUser = this.careUserRepository.create({
      externalId: command.externalId,
      name: command.name,
      crmIndividualUuid: command.crmIndividualUuid,
      email: command.email,
      phone: command.phone,
      mobilePhone: command.mobilePhone,
      socialSecurityNumber: command.socialSecurityNumber,
      address: command.address?.parse()
    })

    await transaction(this.dataSource, async () => {
      await this.careUserRepository.insert(careUser)
      await this.eventEmitter.emitOne(new CareUserCreatedEvent(careUser.uuid))
    })

    return new CreateCareUserResponse(careUser)
  }
}
