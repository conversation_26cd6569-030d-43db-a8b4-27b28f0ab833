import { randomUUID } from 'crypto'
import { AddressCommandBuilder } from '../../../../../utils/address/address-command.builder.js'
import { AddressCommand } from '../../../../../utils/address/address-command.js'
import { CreateCareUserCommand } from './create-care-user.command.js'

export class CreateCareUserCommandBuilder {
  private command: CreateCareUserCommand

  constructor () {
    this.command = new CreateCareUserCommand()
    this.command.name = 'Care User'
    this.command.externalId = randomUUID()
    this.command.crmIndividualUuid = null
    this.command.email = null
    this.command.phone = null
    this.command.mobilePhone = null
    this.command.socialSecurityNumber = null
    this.command.address = new AddressCommandBuilder().build()
  }

  withName (name: string): this {
    this.command.name = name
    return this
  }

  withExternalId (externalId: string): this {
    this.command.externalId = externalId
    return this
  }

  withCrmIndividualUuid (crmIndividualUuid: string): this {
    this.command.crmIndividualUuid = crmIndividualUuid
    return this
  }

  withEmail (email: string | null): this {
    this.command.email = email
    return this
  }

  withPhone (phone: string | null): this {
    this.command.phone = phone
    return this
  }

  withMobilePhone (mobilePhone: string | null): this {
    this.command.mobilePhone = mobilePhone
    return this
  }

  withSocialSecurityNumber (socialSecurityNumber: string | null): this {
    this.command.socialSecurityNumber = socialSecurityNumber
    return this
  }

  withAddress (address: AddressCommand | null): this {
    this.command.address = address
    return this
  }

  build (): CreateCareUserCommand {
    return this.command
  }
}
