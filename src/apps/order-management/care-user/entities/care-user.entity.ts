import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm'
import { AddressColumn } from '../../../../utils/address/address-column.js'
import { Address } from '../../../../utils/address/address.js'

@Entity()
export class CareUser {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @Index()
  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @Column({ type: 'varchar', unique: true, nullable: true })
  externalId: string | null

  @Index()
  @Column({ type: 'uuid', unique: true, nullable: true })
  crmIndividualUuid: string | null

  @Column({ type: 'varchar' })
  name: string

  @Column({ type: 'varchar', nullable: true })
  email: string | null

  @Column({ type: 'varchar', nullable: true })
  phone: string | null

  @Column({ type: 'varchar', nullable: true })
  mobilePhone: string | null

  @Column({ type: 'varchar', nullable: true })
  socialSecurityNumber: string | null

  @AddressColumn({ nullable: true })
  address: Address | null

  @Column({ type: 'varchar', nullable: true })
  remarksForPlanner: string | null

  @Column({ type: 'varchar', nullable: true })
  remarksForDriver: string | null
}
