import { randomUUID } from 'crypto'
import { CareUser } from '../entities/care-user.entity.js'
import { AddressBuilder } from '../../../../utils/address/address.builder.js'

export class CareUserEntityBuilder {
  private readonly careUser: CareUser = new CareUser()

  constructor () {
    this.careUser.uuid = randomUUID()
    this.careUser.externalId = randomUUID()
    this.careUser.name = '<PERSON>'
    this.careUser.crmIndividualUuid = null
    this.careUser.address = new AddressBuilder().build()
  }

  withUuid (uuid: string): CareUserEntityBuilder {
    this.careUser.uuid = uuid
    return this
  }

  withExternalId (externalId: string): CareUserEntityBuilder {
    this.careUser.externalId = externalId
    return this
  }

  withCrmIndividualUuid (crmIndividualUuid: string | null): CareUserEntityBuilder {
    this.careUser.crmIndividualUuid = crmIndividualUuid
    return this
  }

  withName (name: string): CareUserEntityBuilder {
    this.careUser.name = name
    return this
  }

  withRemarksForPlanner (remarksForPlanner: string | null): CareUserEntityBuilder {
    this.careUser.remarksForPlanner = remarksForPlanner
    return this
  }

  withRemarksForDriver (remarksForDriver: string | null): CareUserEntityBuilder {
    this.careUser.remarksForDriver = remarksForDriver
    return this
  }

  withCrnIndividualUuid (crmIndividualUuid: string | null): CareUserEntityBuilder {
    this.careUser.crmIndividualUuid = crmIndividualUuid
    return this
  }

  build (): CareUser {
    return this.careUser
  }
}
