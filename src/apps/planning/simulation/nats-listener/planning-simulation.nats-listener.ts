import { Authenticator, credsAuthenticator } from '@nats-io/transport-node'
import { IncomingRequestPreparedEvent } from '../use-cases/mark-as-prepared/incoming-request-prepared.event.js'
import { MarkSimulationAsPreparedUseCase } from '../use-cases/mark-as-prepared/mark-simulation-as-prepared.use-case.js'
import { MarkSimulationAsRegisteredUseCase } from '../use-cases/mark-as-registered/mark-simulation-as-registered.use-case.js'
import { IncomingRequestRegisteredEvent } from '../use-cases/mark-as-registered/incoming-request-registered.event.js'
import { MarkSimulationAsStartedUseCase } from '../use-cases/mark-as-started/mark-simulation-as-started.use-case.js'
import { IncomingRequestStartedEvent } from '../use-cases/mark-as-started/incoming-request-started.event.js'
import { MarkSimulationAsSolvedUseCase } from '../use-cases/mark-as-solved/mark-simulation-as-solved.use-case.js'
import { IncomingRequestSolvedEvent } from '../use-cases/mark-as-solved/incoming-request-solved.event.js'
import { MarkSimulationAsCompletedUseCase } from '../use-cases/mark-as-completed/mark-simulation-as-completed.use-case.js'
import { IncomingRequestCompletedEvent } from '../use-cases/mark-as-completed/incoming-request-completed.event.js'
import { MarkSimulationAsFailedUseCase } from '../use-cases/mark-as-failed/mark-simulation-as-failed.use-case.js'
import { IncomingRequestFailedEvent } from '../use-cases/mark-as-failed/incomming-request-failed.event.js'
import { IntegrationEvent } from '../../../../modules/integration-events/integration-event.js'
import { NatsSubscriber } from '../../../../modules/nats/nats-application/subscribers/nats-subscriber.decorator.js'
import { NatsClient } from '../../../../modules/nats/nats-application/clients/nats-client.decorator.js'
import { NatsMessageData } from '../../../../modules/nats/nats-application/parameters/nats-message-data.decorator.js'
import { NatsMsgDataJsonPipe } from '../../../../modules/nats/nats-application/parameters/pipes/nats-message-data-json.pipe.js'
import { natsSubject } from '../../../../modules/nats/nats-application/nats-subject.js'
import { OnNatsMessage } from '../../../../modules/nats/nats-application/message-handler/on-nats-message.decorator.js'

@NatsClient((configService) => {
  const host = configService.getOrThrow<string>('NATS_HOST')
  const port = configService.getOrThrow<string>('NATS_PORT')
  const nkey = configService.get<string>('NATS_NKEY')
  let authenticator: Authenticator | undefined

  if (nkey !== undefined) {
    const encodedKey = new TextEncoder().encode(Buffer.from(nkey, 'base64').toString())
    authenticator = credsAuthenticator(encodedKey)
  }

  return {
    servers: `nats://${host}:${port}`,
    authenticator,
    timeout: 3000
  }
})
export class PlanningSimulationNatsClient {}

const PLANNING_SIMULATION_REQUESTS_SUBJECT = '{env}.th.planning-optimisation.requests.>'

@NatsSubscriber(configService => ({
  client: PlanningSimulationNatsClient,
  subject: natsSubject(PLANNING_SIMULATION_REQUESTS_SUBJECT, {
    env: configService.getOrThrow<string>('NODE_ENV')
  })
}))
export class PlanningSimulationNatsSubscriber {
  constructor (
    private readonly requestRegisteredHandler: MarkSimulationAsRegisteredUseCase,
    private readonly requestPreparedHandler: MarkSimulationAsPreparedUseCase,
    private readonly requestStartedHandler: MarkSimulationAsStartedUseCase,
    private readonly requestSolvedHandler: MarkSimulationAsSolvedUseCase,
    private readonly requestCompletedHandler: MarkSimulationAsCompletedUseCase,
    private readonly requestFailedHandler: MarkSimulationAsFailedUseCase
  ) {}

  @OnNatsMessage()
  handleEvent (
    @NatsMessageData(NatsMsgDataJsonPipe) event: IntegrationEvent
  ): Promise<void> | void {
    switch (event.type) {
      case IncomingRequestRegisteredEvent.TYPE: return this.handleRequestRegisteredEvent(event)
      case IncomingRequestPreparedEvent.TYPE: return this.handleRequestPreparedEvent(event)
      case IncomingRequestStartedEvent.TYPE: return this.handleRequestStartedEvent(event)
      case IncomingRequestSolvedEvent.TYPE: return this.handleRequestSolvedEvent(event)
      case IncomingRequestCompletedEvent.TYPE: return this.handleRequestCompletedEvent(event)
      case IncomingRequestFailedEvent.TYPE: return this.handleRequestFailedEvent(event)
      default: throw new Error(`Unknown type: ${event.type}`)
    }
  }

  async handleRequestRegisteredEvent (event: IntegrationEvent): Promise<void> {
    const requestRegisteredEvent = new IncomingRequestRegisteredEvent(event)
    await this.requestRegisteredHandler.execute(requestRegisteredEvent)
  }

  async handleRequestPreparedEvent (event: IntegrationEvent): Promise<void> {
    const requestPreparedEvent = new IncomingRequestPreparedEvent(event)
    await this.requestPreparedHandler.execute(requestPreparedEvent)
  }

  async handleRequestStartedEvent (event: IntegrationEvent): Promise<void> {
    const requestStartedEvent = new IncomingRequestStartedEvent(event)
    await this.requestStartedHandler.execute(requestStartedEvent)
  }

  async handleRequestSolvedEvent (event: IntegrationEvent): Promise<void> {
    const requestSolvedEvent = new IncomingRequestSolvedEvent(event)
    await this.requestSolvedHandler.execute(requestSolvedEvent)
  }

  async handleRequestCompletedEvent (event: IntegrationEvent): Promise<void> {
    const requestCompletedEvent = new IncomingRequestCompletedEvent(event)
    await this.requestCompletedHandler.execute(requestCompletedEvent)
  }

  async handleRequestFailedEvent (event: IntegrationEvent): Promise<void> {
    const requestFailedEvent = new IncomingRequestFailedEvent(event)
    await this.requestFailedHandler.execute(requestFailedEvent)
  }
}
