import { <PERSON><PERSON>ty<PERSON><PERSON><PERSON> } from 'typeorm'
import dayjs from 'dayjs'
import { Simulation } from '../../../entities/simulation.entity.js'
import { Branch } from '../../../../../resource-management/branch/branch.entity.js'
import { BranchEntityBuilder } from '../../../../../resource-management/branch/builders/branch.entity.builder.js'
import { DriverEntityBuilder } from '../../../../../resource-management/driver/entities/driver.entity.builder.js'
import { Driver } from '../../../../../resource-management/driver/entities/driver.entity.js'
import { ShiftEntityBuilder } from '../../../../../resource-management/shift/builders/shift.entity.builder.js'
import { Shift } from '../../../../../resource-management/shift/entities/shift.entity.js'
import { SimulationStatus } from '../../../enums/simulation-status.enum.js'
import { SimulatedPlanningSequenceEntityBuilder } from '../../../entity-builders/simulated-planning-sequence-entity.builder.js'
import { SimulatedPlanningSequence } from '../../../entities/simulated-planning-sequence.entity.js'
import { SimulatedShiftBuilder } from '../../../entity-builders/simulated-shift.builder.js'
import { SimulatedShift } from '../../../entities/simulated-shift.entity.js'
import { SimulationBuilder } from '../../../entity-builders/simulation.builder.js'
import { DateFormats } from '../../../../../../utils/dates/date-formats.js'
import { CareUserEntityBuilder } from '../../../../../order-management/care-user/builders/care-user.entity.builder.js'
import { CareUser } from '../../../../../order-management/care-user/entities/care-user.entity.js'
import { ContractType } from '../../../../../order-management/contract-type/entities/contract-type.entity.js'
import { AcceptedTransportOrderEntityBuilder } from '../../../../../order-management/accepted-transport-order/builders/accepted-transport-order.entity.builder.js'
import { AcceptedTransportOrder } from '../../../../../order-management/accepted-transport-order/entities/accepted-transport-order.entity.js'
import { ContractTypeEntityBuilder } from '../../../../../order-management/contract-type/builders/contract-type.entity.builder.js'
import { SimulatedPlanningAction } from '../../../entities/simulated-planning-action.entity.js'
import { SimulatedTransportOrder } from '../../../entities/simulated-transport-order.entity.js'
import { SimulatedPlanningActionEntityBuilder } from '../../../entity-builders/simulated-planning-action-entity.builder.js'
import { SimulatedTransportOrderBuilder } from '../../../entity-builders/simulated-transport-order.builder.js'
import { LocationBuilder } from '../../../../../../modules/location/location.builder.js'
import { Location } from '../../../../../../modules/location/entities/location.entity.js'
import { MaxTimeInVehicleFormula } from '../../../../../order-management/max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'
import { MaxTimeInVehicleFormulaEntityBuilder } from '../../../../../order-management/max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.builder.js'
import { ClientId } from '../../../../../order-management/client/client-id.js'
import { ClientType } from '../../../../../order-management/client/client-type.js'
import { DriverDefaultShiftEntityBuilder } from '../../../../../resource-management/driver-default-shift/builders/driver-default-shift-entity.builder.js'
import { DriverDefaultShift } from '../../../../../resource-management/driver-default-shift/entities/driver-default-shift.entity.js'
import { Coordinates } from '../../../../../../utils/coordinates/coordinates.js'
import { ContractEntityBuilder } from '../../../../../order-management/contract/entities/contract.entity.builder.js'
import { Contract } from '../../../../../order-management/contract/entities/contract.entity.js'
import { UserEntityBuilder } from '../../../../../../app/users/tests/user-entity.builder.js'
import { User } from '../../../../../../app/users/entities/user.entity.js'
import { PricingFormula } from '../../../../../pricing/pricing-formula/entities/pricing-formula.entity.js'
import { PricingFormulaEntityBuilder } from '../../../../../pricing/pricing-formula/pricing-formula.entity.builder.js'
import { AddressBuilder } from '../../../../../../utils/address/address.builder.js'

export interface GetPlanningActionsTestContext {
  simulation: Simulation
  planningSequence: SimulatedPlanningSequence
}

export async function setupGetPlanningActionsTestContext (
  entityManager: EntityManager
): Promise<GetPlanningActionsTestContext> {
  const user = new UserEntityBuilder().build()

  const location = new LocationBuilder()
    .withAddress(new AddressBuilder()
      .withCoordinates(new Coordinates(0, 0))
      .build())
    .build()

  const branch = new BranchEntityBuilder()
    .build()

  const defaultShift = new DriverDefaultShiftEntityBuilder()
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  const driver = new DriverEntityBuilder()
    .withBranchUuid(branch.uuid)
    .withFirstName('aa')
    .withLastName('aa')
    .withDefaultShiftUuid(defaultShift.uuid)
    .build()

  const shift = new ShiftEntityBuilder()
    .withBranchUuid(branch.uuid)
    .withDriverUuid(driver.uuid)
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .build()

  const simulation = new SimulationBuilder()
    .forDate(dayjs().format(DateFormats.POSTGRES_DATE))
    .withStatus(SimulationStatus.PENDING)
    .withAverageOrdersPerShift(5)
    .withMaxTimeInVehicleMargin(10)
    .withPickupAndDropOffWindowMargin(10)
    .withSecondsPerIteration(5)
    .withTravelTimeMargin(5)
    .withAvailableDrivers(1)
    .withOrdersInSimulation(1)
    .withCreatorUuid(user.uuid)
    .build()

  const simulatedShift = new SimulatedShiftBuilder()
    .withFrom(dayjs().toDate())
    .withUntil(dayjs().toDate())
    .withlicensePlate('1-ABC-123')
    .withStartLocationUuid(location.uuid)
    .withStopLocationUuid(location.uuid)
    .withDriverUuid(driver.uuid)
    .withBranchUuid(branch.uuid)
    .withSimulationUuid(simulation.uuid)
    .withOriginalShiftUuid(shift.uuid)
    .build()

  const planningSequence = new SimulatedPlanningSequenceEntityBuilder()
    .withSimulatedShiftUuid(simulatedShift.uuid)
    .build()

  const timeInVehicleFormula = new MaxTimeInVehicleFormulaEntityBuilder()
    .build()
  await entityManager.insert(MaxTimeInVehicleFormula, timeInVehicleFormula)

  const pricingFormula = new PricingFormulaEntityBuilder().build()
  await entityManager.insert(PricingFormula, pricingFormula)

  const contractType = new ContractTypeEntityBuilder()
    .withMaxTimeInVehicleFormulaUuid(timeInVehicleFormula.uuid)
    .withPricingFormulaUuid(pricingFormula.uuid)
    .build()

  const careUser = new CareUserEntityBuilder()
    .build()

  const contract = new ContractEntityBuilder()
    .withContractTypeUuid(contractType.uuid)
    .withClientId(new ClientId(careUser.uuid, ClientType.CARE_USER))
    .build()

  const order = new AcceptedTransportOrderEntityBuilder()
    .withClientId(new ClientId(careUser.uuid, ClientType.CARE_USER))
    .withCareUserUuid(careUser.uuid)
    .withContractUuid(contract.uuid)
    .withPickupLocationUuid(location.uuid)
    .withDropOffLocationUuid(location.uuid)
    .withFormulaUuid(timeInVehicleFormula.uuid)
    .build()

  const simulatedOrder = new SimulatedTransportOrderBuilder()
    .withSimulationUuid(simulation.uuid)
    .withOriginalOrderUuid(order.uuid)
    .withId(order.id)
    .withDate(order.date)
    .withDescription(order.description)
    .withTargetAction(order.targetAction)
    .withTargetTime(order.targetTime)
    .withArrivalWindowFrom(order.arrivalWindowFrom)
    .withArrivalWindowUntil(order.arrivalWindowUntil)
    .withDemandNormalSeats(order.seatsDemand.normalSeats)
    .withDemandWheelChairSeats(order.seatsDemand.wheelChairSeats)
    .withDemandChildSeats(order.seatsDemand.childSeats)
    .withPickupLocationUuid(order.pickupLocationUuid)
    .withPickupServiceTime(order.pickupServiceTime)
    .withDropOffLocationUuid(order.dropOffLocationUuid)
    .withDropOffServiceTime(order.dropOffServiceTime)
    .withClientId(order.clientId)
    .withCareUserUuid(order.careUserUuid)
    .withContractUuid(order.contractUuid)
    .withFormulaUuid(timeInVehicleFormula.uuid)
    .build()

  const planningAction = new SimulatedPlanningActionEntityBuilder()
    .withSimulatedPlanningSequenceUuid(planningSequence.uuid)
    .withSimulatedOrderUuid(simulatedOrder.uuid)
    .withFromLocationUuid(location.uuid)
    .withToLocationUuid(location.uuid)
    .build()

  await Promise.all([
    entityManager.insert(User, user),
    entityManager.insert(Branch, branch),
    entityManager.insert(Location, location),
    entityManager.insert(ContractType, contractType),
    entityManager.insert(Simulation, simulation)
  ])

  await entityManager.insert(DriverDefaultShift, defaultShift)
  await entityManager.insert(Driver, driver)
  await entityManager.insert(Shift, shift)
  await entityManager.insert(SimulatedShift, simulatedShift)
  await entityManager.insert(SimulatedPlanningSequence, planningSequence)
  await entityManager.insert(CareUser, careUser)
  await entityManager.insert(Contract, contract)
  await entityManager.insert(AcceptedTransportOrder, order)
  await entityManager.insert(SimulatedTransportOrder, simulatedOrder)
  await entityManager.insert(SimulatedPlanningAction, planningAction)

  return {
    simulation,
    planningSequence
  }
}
