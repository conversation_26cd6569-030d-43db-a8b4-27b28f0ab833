import { DynamicModule, Module } from '@nestjs/common'
import { AppModule } from '../../app.module.js'
import { PublishNatsEventJobModule } from '../nats/outbox/publish-nats-event/publish-nats-event.module.js'
import { SyncTypesenseJobModule } from '../typesense/use-cases/sync-collection/sync-typesense-collection-job.module.js'
import { AssignDefaultNotificationPreferencesToUserJobModule } from '../notification/use-cases/assign-default-notification-preferences-to-user/assign-default-notification-preferences-to-user.job.module.js'
import { CreateNotificationJobModule } from '../notification/use-cases/create-notification/create-notification.job.module.js'
import { CreateUserNotificationsJobModule } from '../notification/use-cases/create-user-notifications/create-user-notifications.job-module.js'
import { AddNewNotificationTypeToPreferencesJobModule } from '../notification/use-cases/add-new-notification-type-to-preferences/add-new-notification-type-to-preferences.job.module.js'
import { ProcessAtoChangesJobModule } from '../../apps/planning/simulation/use-cases/process-ato-changes/process-ato-changes-job.module.js'
import { ProcessSimulationResultModule } from '../../apps/planning/simulation/use-cases/process-result/process-simulation-result.module.js'
import { ProcessShiftChangesJobModule } from '../../apps/planning/simulation/use-cases/process-shift-changes/process-shift-changes.job.module.js'
import { RequestSimulationJobModule } from '../../apps/planning/simulation/use-cases/request-simulation/request-simulation.job.module.js'
import { HideShiftsJobModule } from '../../apps/resource-management/shift/use-cases/hide-shift/hide-shifts.job.module.js'
import { RevealShiftsJobModule } from '../../apps/resource-management/shift/use-cases/reveal-shifts/reveal-shifts.job.module.js'
import { SeedDriverShiftsJobModule } from '../../apps/resource-management/shift/use-cases/seed-driver-shifts/seed-driver-shifts.job.module.js'
import { SyncHolidaysLookupJobModule } from '../holidays/jobs/sync-holidays-lookup/sync-holidays-lookup-job.module.js'

@Module({})
export class SystemWorkerModule {
  static forRoot (modules: DynamicModule[]): DynamicModule {
    return {
      module: SystemWorkerModule,
      imports: [
        AppModule.forRoot(modules),
        PublishNatsEventJobModule,
        SyncTypesenseJobModule,
        CreateNotificationJobModule,
        CreateUserNotificationsJobModule,
        AddNewNotificationTypeToPreferencesJobModule,
        AssignDefaultNotificationPreferencesToUserJobModule,
        ProcessAtoChangesJobModule,
        ProcessSimulationResultModule,
        ProcessShiftChangesJobModule,
        RequestSimulationJobModule,
        HideShiftsJobModule,
        RevealShiftsJobModule,
        SeedDriverShiftsJobModule,
        SyncHolidaysLookupJobModule
      ]
    }
  }
}
