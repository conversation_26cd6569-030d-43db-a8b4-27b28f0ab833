import { Location } from '../entities/location.entity.js'
import { LocationType } from '../location-type.enum.js'

export class TypesenseLocation {
  id: string
  name: string
  type: LocationType
  country: string | undefined
  city: string | undefined
  postalCode: string | undefined
  streetName: string | undefined
  streetNumber: string | undefined
  unit: string | undefined
  coordinates: [number, number] | undefined

  constructor (location: Location) {
    this.id = location.uuid
    this.name = location.name
    this.type = location.type
    this.country = location.address.country ?? undefined
    this.city = location.address.city ?? undefined
    this.postalCode = location.address.postalCode ?? undefined
    this.streetName = location.address.streetName ?? undefined
    this.streetNumber = location.address.streetNumber ?? undefined
    this.unit = location.address.unit ?? undefined
    const coordinates = location.address.coordinates
    this.coordinates = coordinates
      ? [coordinates.latitude, coordinates.longitude]
      : undefined
  }
}
