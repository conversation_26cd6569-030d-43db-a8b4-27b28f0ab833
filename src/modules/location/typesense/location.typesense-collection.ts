import { TypesenseCollectionName } from '../../typesense/collections/typesense-collection-name.enum.js'
import { RegisterTypesenseCollection } from '../../typesense/collections/typesense-collection.decorator.js'
import { TypesenseCollection, TypesenseField } from '../../typesense/collections/typesense.collection.js'

@RegisterTypesenseCollection(TypesenseCollectionName.LOCATION)
export class LocationTypesenseCollection extends TypesenseCollection {
  readonly name = TypesenseCollectionName.LOCATION

  readonly searchableFields = [
    { name: 'name', type: 'string', sort: true, infix: true },
    { name: 'coordinates', type: 'geopoint', optional: true }
  ] as const

  readonly filterableFields: TypesenseField[] = [
    { name: 'type', type: 'string', sort: false, facet: true },
    { name: 'country', type: 'string', optional: true, index: false },
    { name: 'city', type: 'string', optional: true, index: false },
    { name: 'postalCode', type: 'string', optional: true, index: true },
    { name: 'streetName', type: 'string', optional: true, index: false },
    { name: 'streetNumber', type: 'string', optional: true, index: false },
    { name: 'unit', type: 'string', optional: true, index: false }
  ] as const

  readonly referenceFields = [] as const
}
