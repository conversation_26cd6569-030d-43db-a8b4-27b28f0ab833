import { ApiProperty } from '@nestjs/swagger'
import { LocationType, LocationTypeApiProperty } from '../../location-type.enum.js'
import { Location } from '../../entities/location.entity.js'
import { AddressResponse } from '../../../../utils/address/address-response.js'

export class GetLocationResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  @LocationTypeApiProperty()
  type: LocationType

  @ApiProperty({ type: AddressResponse })
  address: AddressResponse

  constructor (location: Location) {
    this.uuid = location.uuid
    this.name = location.name
    this.type = location.type
    this.address = new AddressResponse(location.address)
  }
}
