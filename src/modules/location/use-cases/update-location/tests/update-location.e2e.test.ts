import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { DataSource } from 'typeorm'
import { HttpStatus } from '@nestjs/common'
import { UpdateLocationCommandBuilder } from '../update-location.command.builder.js'
import { LocationType } from '../../../location-type.enum.js'
import { LocationBuilder } from '../../../location.builder.js'
import { Location } from '../../../entities/location.entity.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { AddressBuilder } from '../../../../../utils/address/address.builder.js'

describe('Update Locatoin e2e tests', () => {
  let testSetup: EndToEndTestSetup
  let adminUser: TestUser
  let dataSource: DataSource

  before(async () => {
    testSetup = await TestBench.setupEndToEndTest()
    adminUser = await testSetup.authContext.getAdminUser()
    dataSource = testSetup.dataSource
  })

  after(async () => {
    await testSetup.teardown()
  })

  it('Update a location successfully', async () => {
    const location = new LocationBuilder()
      .withName('Findable')
      .withType(LocationType.DAY_CENTRE)
      .withAddress(new AddressBuilder()
        .withPostalCode('3440')
        .build())
      .build()

    await dataSource.manager.insert(Location, location)

    const command = new UpdateLocationCommandBuilder().build()

    const response = await request(testSetup.app.getHttpServer())
      .post(`/locations/${location.uuid}`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(command)

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
