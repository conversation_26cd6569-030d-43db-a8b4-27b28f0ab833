import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { CreateLocationCommandBuilder } from '../create-location.command.builder.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'

describe('Create Location e2e tests', () => {
  let testSetup: EndToEndTestSetup
  let adminUser: TestUser

  before(async () => {
    testSetup = await TestBench.setupEndToEndTest()
    adminUser = await testSetup.authContext.getAdminUser()
  })

  after(async () => {
    await testSetup.teardown()
  })

  it('Creates a new location successfully', async () => {
    const command = new CreateLocationCommandBuilder().build()

    const response = await request(testSetup.app.getHttpServer())
      .post(`/locations`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(command)

    expect(response).toHaveStatus(201)
    expect(response.body).toStrictEqual(expect.objectContaining({
      uuid: expect.uuid(),
      name: command.name,
      type: command.type,
      address: {
        country: command.address.country,
        streetName: command.address.streetName,
        streetNumber: command.address.streetNumber,
        unit: command.address.unit,
        city: command.address.city,
        postalCode: command.address.postalCode,
        coordinates: {
          longitude: command.address.coordinates.longitude,
          latitude: command.address.coordinates.latitude
        }
      }
    }))
  })
})
