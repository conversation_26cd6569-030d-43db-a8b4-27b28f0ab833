import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { Location } from '../../entities/location.entity.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { CreateLocationResponse } from './create-location.response.js'
import { CreateLocationCommand } from './create-location.command.js'
import { LocationCreatedEvent } from './location-created.event.js'

@Injectable()
export class CreateLocationUseCase {
  constructor (
    private readonly datasource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(Location) private readonly locationRepository: Repository<Location>
  ) {}

  async createLocation (command: CreateLocationCommand): Promise<CreateLocationResponse> {
    const location = this.locationRepository.create({
      name: command.name,
      type: command.type,
      address: command.address.parse()
    })

    await transaction(this.datasource, async () => {
      await this.locationRepository.insert(location)
      await this.eventEmitter.emitOne(new LocationCreatedEvent(location))
    })

    return new CreateLocationResponse(location)
  }
}
