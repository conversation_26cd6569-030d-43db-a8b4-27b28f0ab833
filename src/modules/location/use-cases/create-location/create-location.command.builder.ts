import { randomUUID } from 'crypto'
import { LocationType } from '../../location-type.enum.js'
import { AddressCommandBuilder } from '../../../../utils/address/address-command.builder.js'
import { AddressCommand } from '../../../../utils/address/address-command.js'
import { CreateLocationCommand } from './create-location.command.js'

export class CreateLocationCommandBuilder {
  private command: CreateLocationCommand

  constructor () {
    this.command = new CreateLocationCommand()
    this.command.name = randomUUID()
    this.command.type = LocationType.OTHER
    this.command.address = new AddressCommandBuilder().build()
  }

  withName (name: string): this {
    this.command.name = name
    return this
  }

  withType (type: LocationType): this {
    this.command.type = type
    return this
  }

  withAddress (address: AddressCommand): this {
    this.command.address = address
    return this
  }

  build (): CreateLocationCommand {
    return this.command
  }
}
