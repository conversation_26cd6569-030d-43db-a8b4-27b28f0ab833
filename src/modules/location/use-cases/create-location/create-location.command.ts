import { IsEnum, IsNotEmpty, IsObject, IsString, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { LocationType, LocationTypeApiProperty } from '../../location-type.enum.js'
import { AddressCommand } from '../../../../utils/address/address-command.js'

export class CreateLocationCommand {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  name: string

  @LocationTypeApiProperty()
  @IsEnum(LocationType)
  type: LocationType

  @ApiProperty({ type: AddressCommand })
  @IsObject()
  @Type(() => AddressCommand)
  @ValidateNested()
  address: AddressCommand
}
