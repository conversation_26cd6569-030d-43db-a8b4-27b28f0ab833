import { ApiProperty } from '@nestjs/swagger'
import { Location } from '../../entities/location.entity.js'
import { LocationType, LocationTypeApiProperty } from '../../location-type.enum.js'
import { AddressResponse } from '../../../../utils/address/address-response.js'
import { AddressBuilder } from '../../../../utils/address/address.builder.js'

export class CreateLocationResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  @LocationTypeApiProperty()
  type: LocationType

  @ApiProperty({ type: AddressResponse })
  address: AddressResponse

  constructor (location: Location) {
    this.uuid = location.uuid
    this.name = location.name
    this.type = location.type
    this.address = new AddressResponse(
      new AddressBuilder()
        .withCountry(location.address.country)
        .withCity(location.address.city)
        .withPostalCode(location.address.postalCode)
        .withStreetName(location.address.streetName)
        .withStreetNumber(location.address.streetNumber)
        .withUnit(location.address.unit)
        .withCoordinates(location.address.coordinates)
        .build()
    )
  }
}
