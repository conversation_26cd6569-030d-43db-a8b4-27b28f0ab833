import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { DataSource } from 'typeorm'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { LocationBuilder } from '../../../location.builder.js'
import { LocationType } from '../../../location-type.enum.js'
import { Location } from '../../../entities/location.entity.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'

import { LocationTypeCount } from '../location-type.count.js'
import { AddressBuilder } from '../../../../../utils/address/address.builder.js'

describe('Get location e2e tests', () => {
  let testSetup: EndToEndTestSetup
  let adminUser: TestUser
  let dataSource: DataSource

  before(async () => {
    testSetup = await TestBench.setupEndToEndTest()
    adminUser = await testSetup.authContext.getAdminUser()
    dataSource = testSetup.dataSource
  })

  after(async () => {
    await testSetup.teardown()
  })

  it('Retrieves location types successfully', async () => {
    const location = new LocationBuilder()
      .withName('Findable')
      .withType(LocationType.DAY_CENTRE)
      .withAddress(new AddressBuilder()
        .withPostalCode('3440')
        .build())
      .build()

    await dataSource.manager.insert(Location, location)

    const response = await request(testSetup.app.getHttpServer())
      .get(`/locations-types`)
      .set('Authorization', `Bearer ${adminUser.token}`)

    expect(response).toHaveStatus(200)
    expect(response.body.items).toHaveLength(Object.values(LocationType).length)

    const dayCentre = (response.body.items as LocationTypeCount[])
      .find(item => item.type === LocationType.DAY_CENTRE)

    expect(dayCentre?.count).toEqual(1)
  })
})
