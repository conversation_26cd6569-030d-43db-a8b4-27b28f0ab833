import { ApiProperty } from '@nestjs/swagger'
import {
  PaginatedOffsetResponse
} from '../../../../utils/pagination/offset/paginated-offset.response.js'
import { TypesenseLocation } from '../../typesense/typesense-location.js'
import { LocationType, LocationTypeApiProperty } from '../../location-type.enum.js'
import { Coordinates } from '../../../../utils/coordinates/coordinates.js'
import { AddressResponse } from '../../../../utils/address/address-response.js'
import { AddressBuilder } from '../../../../utils/address/address.builder.js'

class GetLocationResponseItem {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  @LocationTypeApiProperty()
  type: LocationType

  @ApiProperty({ type: AddressResponse })
  address: AddressResponse

  constructor (location: TypesenseLocation) {
    this.uuid = location.id
    this.name = location.name
    this.type = location.type

    this.address = new AddressResponse(
      new AddressBuilder()
        .withCity(location.city)
        .withCountry(location.country)
        .withPostalCode(location.postalCode)
        .withStreetName(location.streetName)
        .withStreetNumber(location.streetNumber)
        .withUnit(location.unit)
        .withCoordinates(
          location.coordinates
            ? new Coordinates(location.coordinates[0], location.coordinates[1])
            : null
        )
        .build()
    )
  }
}

export class GetLocationsResponse extends PaginatedOffsetResponse<GetLocationResponseItem> {
  @ApiProperty({ type: GetLocationResponseItem, isArray: true })
  declare items: GetLocationResponseItem[]

  constructor (locations: PaginatedOffsetResponse<TypesenseLocation>) {
    const locationItems = locations.items.map(location => new GetLocationResponseItem(location))
    super(locationItems, locations.meta)
  }
}
