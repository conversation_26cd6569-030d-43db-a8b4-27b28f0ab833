import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { stringify } from 'qs'
import { expect } from 'expect'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { TypesenseCollectionService } from '../../../../typesense/services/typesense-collection.service.js'
import { LocationBuilder } from '../../../location.builder.js'
import { GetLocationsQueryBuilder } from '../query/get-locations.query.builder.js'
import { LocationType } from '../../../location-type.enum.js'
import { SortDirection } from '../../../../../utils/query/search.query.js'
import { GetLocationsSortQueryKey } from '../query/get-locations-sort-query-key.enum.js'
import { Coordinates } from '../../../../../utils/coordinates/coordinates.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TypesenseCollectionName } from '../../../../typesense/collections/typesense-collection-name.enum.js'
import { MigrateCollectionsUseCase } from '../../../../typesense/use-cases/migrate-collections/migrate-collections.use-case.js'
import { AddressBuilder } from '../../../../../utils/address/address.builder.js'

describe('Get locations e2e tests', () => {
  let testSetup: EndToEndTestSetup
  let adminUser: TestUser
  let typesenseCollectionService: TypesenseCollectionService

  before(async () => {
    testSetup = await TestBench.setupEndToEndTest()
    adminUser = await testSetup.authContext.getAdminUser()

    const migrator = testSetup.testModule.get(MigrateCollectionsUseCase)
    await migrator.execute(true, [TypesenseCollectionName.LOCATION])

    typesenseCollectionService = testSetup.testModule.get(
      TypesenseCollectionService, { strict: false })
  })

  after(async () => {
    await testSetup.teardown()
  })

  it('Retrieves locations successfully', async () => {
    const findableLocation = new LocationBuilder()
      .withName('Findable')
      .withType(LocationType.DAY_CENTRE)
      .withAddress(new AddressBuilder()
        .withPostalCode('3440')
        .build())
      .build()
    const unfindableByNameLocation = new LocationBuilder()
      .withName('Hidden')
      .withType(LocationType.DAY_CENTRE)
      .withAddress(new AddressBuilder()
        .withPostalCode('3440')
        .build())
      .build()
    const unfindableByTypeLocation = new LocationBuilder()
      .withName('Findable')
      .withType(LocationType.DEPOT)
      .withAddress(new AddressBuilder()
        .withPostalCode('3440')
        .build())
      .build()
    const unfindableByZipCodeLocation = new LocationBuilder()
      .withName('Findable')
      .withType(LocationType.DAY_CENTRE)
      .withAddress(new AddressBuilder()
        .withPostalCode('1000')
        .build())
      .build()

    await typesenseCollectionService.importManually(
      TypesenseCollectionName.LOCATION,
      [
        findableLocation,
        unfindableByNameLocation,
        unfindableByTypeLocation,
        unfindableByZipCodeLocation
      ]
    )

    const query = new GetLocationsQueryBuilder()
      .withSearch(findableLocation.name)
      .withFilter({
        types: [LocationType.DAY_CENTRE],
        postalCode: ['3440']
      })
      .withSort(GetLocationsSortQueryKey.NAME, SortDirection.ASC)
      .build()

    const response = await request(testSetup.app.getHttpServer())
      .get(`/locations`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body).toStrictEqual(expect.objectContaining({
      items: [expect.objectContaining({
        uuid: findableLocation.uuid
      })]
    }))
  })

  it('Retrieves locations successfully based on coordinates', async () => {
    const findableLocation = new LocationBuilder()
      .withName('Findable')
      .withAddress(new AddressBuilder()
        .withCoordinates(new Coordinates(50.8503, 4.3517))
        .build())
      .build()
    const unfindableByCoordinatesLocation = new LocationBuilder()
      .withName('Hidden')
      .withAddress(new AddressBuilder()
        .withCoordinates(new Coordinates(50.8503, 5.3517))
        .build())
      .build()

    await typesenseCollectionService.importManually(
      TypesenseCollectionName.LOCATION,
      [findableLocation, unfindableByCoordinatesLocation]
    )

    const query = new GetLocationsQueryBuilder()
      .withSearch(findableLocation.name)
      .withFilter({
        coordinates: {
          latitude: '50.8503',
          longitude: '4.3517'
        },
        radiusInMeters: '100'
      })
      .withSort(GetLocationsSortQueryKey.COORDINATES, SortDirection.ASC)
      .build()

    const response = await request(testSetup.app.getHttpServer())
      .get(`/locations`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body).toStrictEqual(expect.objectContaining({
      items: [expect.objectContaining({
        uuid: findableLocation.uuid
      })]
    }))
  })
})
