import { Injectable } from '@nestjs/common'
import { TypesenseQueryService } from '../../../typesense/services/typesense-query.service.js'
import { LocationTypesenseCollection } from '../../typesense/location.typesense-collection.js'
import { SortDirection } from '../../../../utils/query/search.query.js'
import { CoordinatesNotProvidedApiError } from '../../coordinates-not-provided.api-error.js'
import { TypesenseCollectionName } from '../../../typesense/collections/typesense-collection-name.enum.js'
import { TypesenseOperationMode } from '../../../typesense/param-builders/enums/typesense-operation-mode.enum.js'
import { TypesenseSearchParamsBuilder } from '../../../typesense/param-builders/search-params.builder.js'
import { GetLocationsQuery } from './query/get-locations.query.js'
import { GetLocationsResponse } from './get-locations.response.js'
import { GetLocationsSortQuery } from './query/get-locations.sort-query.js'
import { GetLocationsSortQueryKey } from './query/get-locations-sort-query-key.enum.js'
import { CoordinatesQuery } from './query/get-locations.filter-query.js'

@Injectable()
export class GetLocationsUseCase {
  constructor (
    private readonly typesense: TypesenseQueryService
  ) {}

  async findLocations (query: GetLocationsQuery): Promise<GetLocationsResponse> {
    const searchParamsBuilder = new TypesenseSearchParamsBuilder<LocationTypesenseCollection>()
      .withQuery(query.search)
      .addSearchOn('name', TypesenseOperationMode.ALWAYS)
      .withLimit(query.pagination?.limit)
      .withOffset(query.pagination?.offset)
      .addFilterOn('type', query.filter?.types)
      .addFilterOn('postalCode', query.filter?.postalCode)
      .addLocationFilterOn('coordinates', query.filter?.coordinates, query.filter?.radiusInMeters)

    if (query.sort != null) {
      this.addSortQueries(searchParamsBuilder, query.sort, query.filter?.coordinates)
    } else {
      searchParamsBuilder.addSortOn('name', SortDirection.ASC)
    }

    const searchParams = searchParamsBuilder.build()

    const searchResult = await this.typesense.search(
      TypesenseCollectionName.LOCATION,
      searchParams
    )

    return new GetLocationsResponse(searchResult)
  }

  private addSortQueries (
    searchParamsBuilder: TypesenseSearchParamsBuilder<LocationTypesenseCollection>,
    sortQueries: GetLocationsSortQuery[],
    coordinatesQuery: CoordinatesQuery | undefined
  ): void {
    sortQueries.forEach((sortQuery) => {
      if (sortQuery.key === GetLocationsSortQueryKey.COORDINATES) {
        if (coordinatesQuery == null) {
          throw new CoordinatesNotProvidedApiError()
        }
        searchParamsBuilder.addLocationSortOn(
          sortQuery.key,
          coordinatesQuery,
          sortQuery.order
        )
      } else {
        searchParamsBuilder.addSortOn(sortQuery.key, sortQuery.order)
      }
    })
  }
}
