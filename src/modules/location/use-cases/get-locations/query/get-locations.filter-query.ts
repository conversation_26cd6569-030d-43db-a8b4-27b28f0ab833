import { ApiProperty } from '@nestjs/swagger'
import {
  IsOptional,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsString,
  ValidateNested,
  IsObject
} from 'class-validator'
import { Type } from 'class-transformer'
import { FilterQuery } from '../../../../../utils/query/search.query.js'
import { LocationType, LocationTypeApiProperty } from '../../../location-type.enum.js'

export class CoordinatesQuery extends FilterQuery {
  @ApiProperty({ type: 'string' })
  @IsNotEmpty()
  longitude: string

  @ApiProperty({ type: 'string' })
  @IsNotEmpty()
  latitude: string
}

export class GetLocationsFilterQuery extends FilterQuery {
  @LocationTypeApiProperty({ isArray: true, required: false })
  @IsOptional()
  @IsArray()
  @IsEnum(LocationType, { each: true })
  types?: LocationType[]

  @ApiProperty({ type: 'string', isArray: true, required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  postalCode?: string[]

  @ApiProperty({ type: CoordinatesQuery, required: false })
  @IsOptional()
  @IsObject()
  @Type(() => CoordinatesQuery)
  @ValidateNested()
  coordinates?: CoordinatesQuery

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  radiusInMeters?: string
}
