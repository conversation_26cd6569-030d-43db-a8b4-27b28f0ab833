import { randomUUID } from 'crypto'
import { AddressBuilder } from '../../utils/address/address.builder.js'
import { Address } from '../../utils/address/address.js'
import { Location } from './entities/location.entity.js'
import { LocationType } from './location-type.enum.js'

export class LocationBuilder {
  private readonly location: Location
  constructor () {
    this.location = new Location()
    this.location.uuid = randomUUID()
    this.location.name = randomUUID()
    this.location.type = LocationType.OTHER
    this.location.address = new AddressBuilder().build()
    this.location.createdAt = new Date()
    this.location.updatedAt = new Date()
  }

  withUuid (uuid: string): this {
    this.location.uuid = uuid
    return this
  }

  withName (name: string): this {
    this.location.name = name
    return this
  }

  withAddress (address: Address): this {
    this.location.address = address
    return this
  }

  withCreatedAt (createdAt: Date): this {
    this.location.createdAt = createdAt
    return this
  }

  withUpdatedAt (updatedAt: Date): this {
    this.location.updatedAt = updatedAt
    return this
  }

  withType (type: LocationType): this {
    this.location.type = type
    return this
  }

  build (): Location {
    return this.location
  }
}
