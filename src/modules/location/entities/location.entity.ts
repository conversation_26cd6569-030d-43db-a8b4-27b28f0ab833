import {
  Column,
  CreateDateColumn,
  Entity, Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm'
import { LocationType } from '../location-type.enum.js'
import { Address } from '../../../utils/address/address.js'
import { AddressColumn } from '../../../utils/address/address-column.js'

@Entity()
export class Location {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @Index()
  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @Column({ type: 'varchar' })
  name: string

  @Column({ type: 'enum', enum: LocationType, default: LocationType.OTHER })
  type: LocationType

  @AddressColumn()
  address: Address
}
