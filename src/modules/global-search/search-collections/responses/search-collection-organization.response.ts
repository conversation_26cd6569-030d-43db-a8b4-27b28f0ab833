import { ApiProperty } from '@nestjs/swagger'
import { Coordinates } from '@wisemen/coordinates'
import { TypesenseOrganization } from '../../../../apps/order-management/organization/typesense/typesense-organization.js'
import { AddressResponse } from '../../../../utils/address/address-response.js'
import { AddressBuilder } from '../../../../utils/address/address.builder.js'

export class SearchCollectionOrganizationResponse {
  @ApiProperty({ format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, nullable: true })
  externalId: string | null

  @ApiProperty({ type: String, format: 'uuid', nullable: true })
  crmBusinessUuid: string | null

  @ApiProperty()
  name: string

  @ApiProperty({ type: String, required: false })
  abbreviation?: string

  @ApiProperty({ type: String, nullable: true })
  vatNumber: string | null

  @ApiProperty({ type: AddressResponse })
  address: AddressResponse

  constructor (organization: TypesenseOrganization) {
    this.uuid = organization.id
    this.name = organization.name
    this.externalId = organization.externalId ?? null
    this.crmBusinessUuid = organization.crmBusinessUuid ?? null
    this.abbreviation = organization.abbreviation
    this.vatNumber = organization.vatNumber ?? null
    this.address = new AddressResponse(
      new AddressBuilder()
        .withCity(organization.city)
        .withCountry(organization.country)
        .withPostalCode(organization.postalCode)
        .withStreetName(organization.streetName)
        .withStreetNumber(organization.streetNumber)
        .withUnit(organization.unit)
        .withCoordinates(
          organization.coordinates
            ? new Coordinates(organization.coordinates[0], organization.coordinates[1])
            : null
        )
        .build()
    )
  }
}
