import { ApiProperty } from '@nestjs/swagger'
import { Coordinates } from '@wisemen/coordinates'
import { TypesenseCareUser } from '../../../../apps/order-management/care-user/typesense/typesense-care-user.js'
import { AddressResponse } from '../../../../utils/address/address-response.js'
import { AddressBuilder } from '../../../../utils/address/address.builder.js'

export class SearchCollectionCareUserResponse {
  @ApiProperty({ format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, nullable: true })
  externalId: string | null

  @ApiProperty({ type: String, format: 'uuid', nullable: true })
  crmIndividualUuid: string | null

  @ApiProperty()
  name: string

  @ApiProperty({ type: String, nullable: true })
  email: string | null

  @ApiProperty({ type: String, nullable: true })
  phone: string | null

  @ApiProperty({ type: String, nullable: true })
  mobilePhone: string | null

  @ApiProperty({ type: String, nullable: true })
  socialSecurityNumber: string | null

  @ApiProperty({ type: AddressResponse, nullable: true })
  address: AddressResponse | null

  constructor (careUser: TypesenseCareUser) {
    this.uuid = careUser.id
    this.name = careUser.name
    this.externalId = careUser.externalId ?? null
    this.crmIndividualUuid = careUser.crmIndividualUuid
    this.email = careUser.email ?? null
    this.phone = careUser.phone ?? null
    this.mobilePhone = careUser.mobilePhone ?? null
    this.socialSecurityNumber = careUser.socialSecurityNumber ?? null
    this.address = new AddressResponse(
      new AddressBuilder()
        .withCity(careUser.city)
        .withCountry(careUser.country)
        .withPostalCode(careUser.postalCode)
        .withStreetName(careUser.streetName)
        .withStreetNumber(careUser.streetNumber)
        .withUnit(careUser.unit)
        .withCoordinates(
          careUser.coordinates
            ? new Coordinates(careUser.coordinates[0], careUser.coordinates[1])
            : null
        )
        .build()
    )
  }
}
