import fs from 'fs'
import { Injectable } from '@nestjs/common'
import { Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Location } from '../location/entities/location.entity.js'
import { Coordinates } from '../../utils/coordinates/coordinates.js'
import { csvStringToObjectArray } from '../../utils/csv/parse-csv.js'
import { LocationType } from '../location/location-type.enum.js'
import { AddressBuilder } from '../../utils/address/address.builder.js'
import { RawLocationCsvRecord } from './records/raw-location-csv.record.js'

@Injectable()
export class SeedLocationsUseCase {
  constructor (
    @InjectRepository(Location) private readonly locationsRepository: Repository<Location>
  ) {}

  async seed (pathToFile: string): Promise<void> {
    const csv = fs.readFileSync(pathToFile, 'utf-8')
    const records = csvStringToObjectArray<RawLocationCsvRecord>(csv)
    const locations = records.map(record => this.parseRecord(record))

    await this.locationsRepository.upsert(locations, { conflictPaths: { uuid: true } })
  }

  private parseRecord (record: RawLocationCsvRecord): Location {
    const location = new Location()
    location.uuid = record.uuid.trim()
    location.name = record.name

    location.address = new AddressBuilder()
      .withCity(this.parseStringField(record.city))
      .withCountry(this.parseStringField(record.country))
      .withPostalCode(this.parseStringField(record.post_code))
      .withStreetName(this.parseStringField(record.street))
      .withStreetNumber(this.parseStringField(record.street_number))
      .withUnit(this.parseStringField(record.unit_number))
      .withCoordinates(new Coordinates(Number(record.latitude), Number(record.longitude)))
      .build()

    location.type = record.location_type as LocationType
    location.createdAt = new Date()
    location.updatedAt = new Date()

    return location
  }

  private parseStringField (field: string): string | null {
    const trimmedField = field.trim()
    if (trimmedField.length === 0) {
      return null
    } else {
      return trimmedField
    }
  }
}
