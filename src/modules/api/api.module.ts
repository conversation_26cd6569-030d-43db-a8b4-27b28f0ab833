import { MiddlewareConsumer, Module } from '@nestjs/common'
import { AppModule } from '../../app.module.js'
import { AuthModule } from '../auth/auth.module.js'
import { SwaggerModule } from '../swagger/swagger.module.js'
import { StatusModule } from '../status/status.module.js'
import { UserModule } from '../../app/users/user.module.js'
import { RoleModule } from '../../app/roles/role.module.js'
import { PermissionModule } from '../permission/permission.module.js'
import { FileModule } from '../files/file.module.js'
import { LocalizationModule } from '../localization/modules/localization.module.js'
import { UiPreferencesModule } from '../../app/ui-preferences/ui-preferences.module.js'
import { AuthMiddleware } from '../auth/middleware/auth.middleware.js'
import { DomainEventLogModule } from '../domain-event-log/domain-event-log.module.js'
import { OneSignalModule } from '../one-signal/one-signal.module.js'
import { GlobalSearchModule } from '../global-search/global-search.module.js'
import { NotificationModule } from '../notification/notification.module.js'
import { JobsApiModule } from '../jobs/jobs.api-module.js'
import { HoldingModule } from '../../apps/order-management/holding/holding.module.js'
import { AcceptedTransportOrderModule } from '../../apps/order-management/accepted-transport-order/accepted-transport-order.module.js'
import { CareUserContactPersonModule } from '../../apps/order-management/care-user-contact-person/care-user-contact-person.module.js'
import { CareUserModule } from '../../apps/order-management/care-user/care-user.module.js'
import { ContractTypeModule } from '../../apps/order-management/contract-type/contract-type.module.js'
import { MaxTimeInVehicleFormulaModule } from '../../apps/order-management/max-time-in-vehicle-formula/max-time-in-vehicle-formula.module.js'
import { OrganizationContactPersonModule } from '../../apps/order-management/organization-contact-person/organization-contact-person.module.js'
import { OrganizationModule } from '../../apps/order-management/organization/organization.module.js'
import { SimulationModule } from '../../apps/planning/simulation/simulation.module.js'
import { AbsenceModule } from '../../apps/resource-management/absence/absence.module.js'
import { BranchModule } from '../../apps/resource-management/branch/branch.module.js'
import { DriverModule } from '../../apps/resource-management/driver/driver.module.js'
import { ShiftModule } from '../../apps/resource-management/shift/shift.module.js'
import { DatabaseSeederModule } from '../database-seeder/database-seeder.module.js'
import { LocationModule } from '../location/location.module.js'
import { ClientModule } from '../../apps/order-management/client/client-module.js'
import { DepartmentContactPersonModule } from '../../apps/order-management/department-contact-person/department-contact-person.module.js'
import { DepartmentModule } from '../../apps/order-management/department/department.module.js'
import { OrderRequestModule } from '../../apps/order-management/order-request/order-request.module.js'
import { VirtualSegmentModule } from '../../apps/order-management/virtual-segment/virtual-segment.module.js'
import { DriverAvailabilitiesModule } from '../../apps/resource-management/driver-availabilities/driver-availabilities.module.js'
import { ContactPersonModule } from '../../apps/order-management/contact-person/contact-person.module.js'
import { PricingFormulaModule } from '../../apps/pricing/pricing-formula/pricing-formula.module.js'
import { PricingParametersModule } from '../../apps/pricing/pricing-parameters/pricing-parameters.module.js'
import { HolidayModule } from '../holidays/holiday.module.js'

@Module({
  imports: [
    AppModule.forRoot(),
    AuthModule,
    SwaggerModule,
    StatusModule,
    UserModule,
    RoleModule,
    PermissionModule,
    FileModule,
    LocalizationModule,
    UiPreferencesModule,
    OneSignalModule,
    DomainEventLogModule,
    GlobalSearchModule,
    NotificationModule,
    JobsApiModule,
    HoldingModule,
    DatabaseSeederModule,
    DriverModule,
    AcceptedTransportOrderModule,
    ShiftModule,
    ContractTypeModule,
    OrganizationModule,
    OrganizationContactPersonModule,
    SimulationModule,
    AbsenceModule,
    LocationModule,
    BranchModule,
    CareUserModule,
    CareUserContactPersonModule,
    MaxTimeInVehicleFormulaModule,
    DepartmentModule,
    DepartmentContactPersonModule,
    ClientModule,
    OrderRequestModule,
    VirtualSegmentModule,
    DriverAvailabilitiesModule,
    ContactPersonModule,
    PricingParametersModule,
    PricingFormulaModule,
    HolidayModule
  ]
})
export class ApiModule {
  configure (consumer: MiddlewareConsumer): void {
    consumer
      .apply(AuthMiddleware)
      .exclude('auth/token')
      .forRoutes('{*all}')
  }
}
