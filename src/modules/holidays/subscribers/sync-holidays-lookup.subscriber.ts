import { Injectable } from '@nestjs/common'
import { PgBossScheduler } from '@wisemen/pgboss-nestjs-job'
import { Subscribe } from '../../../modules/domain-events/subscribe.decorator.js'
import { HolidayCreatedEvent } from '../use-cases/create-holiday/holiday-created.event.js'
import { SyncHolidaysLookupJob } from '../jobs/sync-holidays-lookup/sync-holidays-lookup.job.js'
import { HolidayUpdatedEvent } from '../use-cases/update-holiday/holiday-updated.event.js'

@Injectable()
export class SyncHolidaysLookupSubscriber {
  constructor (
    private readonly jobScheduler: PgBossScheduler
  ) {}

  @Subscribe(HolidayCreatedEvent)
  @Subscribe(HolidayUpdatedEvent)
  async handle (): Promise<void> {
    const job = new SyncHolidaysLookupJob()
    await this.jobScheduler.scheduleJob(job)
  }
}
