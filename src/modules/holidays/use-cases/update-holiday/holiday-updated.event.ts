import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEventLog } from '../../../../modules/domain-event-log/domain-event-log.entity.js'
import { DomainEventType } from '../../../../modules/domain-events/domain-event-type.js'
import { DomainEvent } from '../../../../modules/domain-events/domain-event.js'
import { RegisterDomainEvent } from '../../../../modules/domain-events/register-domain-event.decorator.js'
import { HolidayUuid } from '../../entities/holiday.uuid.js'

@OneOfMeta(DomainEventLog, DomainEventType.HOLIDAY_UPDATED)
export class HolidayUpdatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly holidayUuid: HolidayUuid

  constructor (
    holidayUuid: HolidayUuid
  ) {
    this.holidayUuid = holidayUuid
  }
}

@RegisterDomainEvent(DomainEventType.HOLIDAY_UPDATED, 1)
export class HolidayUpdatedEvent extends DomainEvent<HolidayUpdatedEventContent> {
  constructor (holidayUuid: HolidayUuid) {
    super({ content: new HolidayUpdatedEventContent(
      holidayUuid
    ) })
  }
}
