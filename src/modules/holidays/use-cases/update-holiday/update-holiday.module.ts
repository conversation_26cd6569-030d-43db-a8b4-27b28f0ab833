import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Holiday } from '../../entities/holiday.entity.js'
import { DomainEventEmitterModule } from '../../../../modules/domain-events/domain-event-emitter.module.js'
import { UpdateHolidayController } from './update-holiday.controller.js'
import { UpdateHolidayUseCase } from './update-holiday.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Holiday
    ]),
    DomainEventEmitterModule
  ],
  controllers: [
    UpdateHolidayController
  ],
  providers: [
    UpdateHolidayUseCase
  ],
  exports: []
})
export class UpdateHolidayModule {}
