import { Body, Controller, HttpCode, HttpStatus, Patch } from '@nestjs/common'
import { ApiNoContentResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { ApiBadRequestErrorResponse, ApiNotFoundErrorResponse } from '../../../../modules/exceptions/api-errors/api-error-response.decorator.js'
import { HolidayNotFoundError } from '../../errors/holiday-not-found.error.js'
import { HolidayNotInFutureError } from '../../errors/holiday-not-in-future.error.js'
import { InvalidHolidayStartDateError } from '../../errors/invalid-holiday-start-date.error.js'
import { InvalidHolidayEndDateError } from '../../errors/invalid-holiday-end-date.error.js'
import { HolidayUuid } from '../../entities/holiday.uuid.js'
import { UpdateHolidayUseCase } from './update-holiday.use-case.js'
import { UpdateHolidayCommand } from './update-holiday.command.js'

@ApiTags('Holidays')
@Controller('holidays/:holidayUuid')
export class UpdateHolidayController {
  constructor (
    private readonly useCase: UpdateHolidayUseCase
  ) {}

  @Patch()
  @HttpCode(HttpStatus.NO_CONTENT)
  @Permissions(Permission.HOLIDAY_UPDATE)
  @ApiNoContentResponse({
    description: 'Holiday updated'
  })
  @ApiNotFoundErrorResponse(HolidayNotFoundError)
  @ApiBadRequestErrorResponse(
    HolidayNotInFutureError,
    InvalidHolidayStartDateError,
    InvalidHolidayEndDateError
  )
  async updateHoliday (
    @UuidParam('holidayUuid') holidayUuid: HolidayUuid,
    @Body() command: UpdateHolidayCommand
  ): Promise<void> {
    await this.useCase.execute(holidayUuid, command)
  }
}
