import { HolidayType } from '../../holiday-type.enum.js'
import { HolidayRecurrence } from '../../holiday-recurrence.enum.js'
import { UpdateHolidayCommand } from './update-holiday.command.js'

export class UpdateHolidayCommandBuilder {
  private command: UpdateHolidayCommand
  constructor () {
    this.command = new UpdateHolidayCommand()
    this.command.name = 'Test Holiday'
    this.command.holidayType = HolidayType.HOLIDAY
    this.command.isRecurring = false
    this.command.startDate = '2025-12-25'
    this.command.endDate = null
    this.command.recurrence = null
  }

  withName (name: string): this {
    this.command.name = name
    return this
  }

  withHolidayType (holidayType: HolidayType): this {
    this.command.holidayType = holidayType
    return this
  }

  withIsRecurring (isRecurring: boolean): this {
    this.command.isRecurring = isRecurring
    return this
  }

  withStartDate (startDate: string): this {
    this.command.startDate = startDate
    return this
  }

  withEndDate (endDate: string | null): this {
    this.command.endDate = endDate
    return this
  }

  withRecurrence (recurrence: HolidayRecurrence | null): this {
    this.command.recurrence = recurrence
    return this
  }

  build (): UpdateHolidayCommand {
    return this.command
  }
}
