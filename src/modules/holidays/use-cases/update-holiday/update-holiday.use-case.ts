import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Repository } from 'typeorm'
import dayjs from 'dayjs'
import { DomainEventEmitter } from '../../../../modules/domain-events/domain-event-emitter.js'
import { Holiday } from '../../entities/holiday.entity.js'
import { HolidayUuid } from '../../entities/holiday.uuid.js'
import { HolidayNotFoundError } from '../../errors/holiday-not-found.error.js'
import { HolidayNotInFutureError } from '../../errors/holiday-not-in-future.error.js'
import { InvalidHolidayStartDateError } from '../../errors/invalid-holiday-start-date.error.js'
import { InvalidHolidayEndDateError } from '../../errors/invalid-holiday-end-date.error.js'
import { UpdateHolidayCommand } from './update-holiday.command.js'
import { HolidayUpdatedEvent } from './holiday-updated.event.js'

@Injectable()
export class UpdateHolidayUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(Holiday)
    private readonly holidayRepository: Repository<Holiday>
  ) {}

  async execute (holidayUuid: HolidayUuid, command: UpdateHolidayCommand): Promise<void> {
    const holiday = await this.holidayRepository.findOne({
      where: { uuid: holidayUuid }
    })

    if (holiday === null) {
      throw new HolidayNotFoundError(holidayUuid)
    }

    this.validateHolidayIsInFuture(holiday)

    this.validateStartInFuture(command.startDate)
    this.validateEndInFuture(command.endDate)

    await transaction(this.dataSource, async () => {
      await this.holidayRepository.update(holiday.uuid, {
        name: command.name,
        startDate: command.startDate,
        endDate: command.endDate,
        isRecurring: command.isRecurring,
        type: command.holidayType,
        recurrence: command.recurrence
      })
      await this.eventEmitter.emitOne(new HolidayUpdatedEvent(holiday.uuid))
    })
  }

  private validateHolidayIsInFuture (holiday: Holiday): void {
    const isInFuture = dayjs(holiday.startDate).isAfter(dayjs())

    if (!isInFuture) {
      throw new HolidayNotInFutureError(holiday.uuid)
    }
  }

  private validateStartInFuture (start: string): void {
    const isStartValid = dayjs(start).isAfter(dayjs())

    if (!isStartValid) {
      throw new InvalidHolidayStartDateError(start)
    }
  }

  private validateEndInFuture (end: string | null): void {
    if (end == null) {
      return
    }
    const isEndValid = dayjs(end).isAfter(dayjs())

    if (!isEndValid) {
      throw new InvalidHolidayEndDateError(end)
    }
  }
}
