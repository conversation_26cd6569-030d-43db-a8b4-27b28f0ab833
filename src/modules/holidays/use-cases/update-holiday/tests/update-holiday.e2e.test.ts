import { before, describe, it, after } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import dayjs from 'dayjs'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { TestAuthContext } from '../../../../../../test/utils/test-auth-context.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { Holiday } from '../../../entities/holiday.entity.js'
import { HolidayType } from '../../../holiday-type.enum.js'
import { HolidayRecurrence } from '../../../holiday-recurrence.enum.js'
import { UpdateHolidayCommandBuilder } from '../update-holiday-command.builder.js'

describe('Update holiday end to end tests', () => {
  let setup: EndToEndTestSetup
  let context: TestAuthContext
  let adminUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    context = setup.authContext

    adminUser = await context.getAdminUser()
  })

  after(async () => await setup.teardown())

  it('updates holiday', async () => {
    const futureDate = dayjs().add(1, 'year').format('YYYY-MM-DD')
    const holiday = setup.dataSource.getRepository(Holiday).create({
      name: 'Original Holiday',
      startDate: futureDate,
      endDate: null,
      isRecurring: false,
      type: HolidayType.HOLIDAY,
      recurrence: null
    })
    await setup.dataSource.getRepository(Holiday).save(holiday)

    const updateHolidayDto = new UpdateHolidayCommandBuilder()
      .withName('Updated Holiday')
      .withStartDate(dayjs().add(2, 'years').format('YYYY-MM-DD'))
      .withIsRecurring(true)
      .withRecurrence(HolidayRecurrence.YEARLY)
      .build()

    const response = await request(setup.httpServer)
      .patch(`/holidays/${holiday.uuid}`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(updateHolidayDto)

    expect(response).toHaveStatus(204)
  })
})
