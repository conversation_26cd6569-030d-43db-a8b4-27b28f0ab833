import { before, describe, it } from 'node:test'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import dayjs from 'dayjs'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { Holiday } from '../../../entities/holiday.entity.js'
import { generateHolidayUuid } from '../../../entities/holiday.uuid.js'
import { HolidayNotFoundError } from '../../../errors/holiday-not-found.error.js'
import { HolidayNotInFutureError } from '../../../errors/holiday-not-in-future.error.js'
import { InvalidHolidayStartDateError } from '../../../errors/invalid-holiday-start-date.error.js'
import { HolidayType } from '../../../holiday-type.enum.js'
import { HolidayUpdatedEvent } from '../holiday-updated.event.js'
import { UpdateHolidayCommandBuilder } from '../update-holiday-command.builder.js'
import { UpdateHolidayUseCase } from '../update-holiday.use-case.js'

describe('UpdateHolidayUseCase Unit test', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('emits an event when updating a holiday', async () => {
    const eventEmitter = createStubInstance(DomainEventEmitter)
    const holidayUuid = generateHolidayUuid()

    const existingHoliday = {
      uuid: holidayUuid,
      name: 'Old Holiday',
      startDate: dayjs().add(1, 'month').format('YYYY-MM-DD'),
      endDate: null,
      isRecurring: false,
      type: HolidayType.HOLIDAY,
      recurrence: null
    } as Holiday

    const repository = createStubInstance(Repository<Holiday>)
    repository.findOne.resolves(existingHoliday)
    repository.save.resolves(existingHoliday)

    const useCase = new UpdateHolidayUseCase(stubDataSource(), eventEmitter, repository)
    const command = new UpdateHolidayCommandBuilder()
      .withName('Updated Holiday')
      .withStartDate(dayjs().add(2, 'months').format('YYYY-MM-DD'))
      .withIsRecurring(false)
      .build()

    await useCase.execute(holidayUuid, command)

    expect(eventEmitter).toHaveEmitted(new HolidayUpdatedEvent(holidayUuid))
  })

  it('throws HolidayNotFoundError when holiday does not exist', () => {
    const eventEmitter = createStubInstance(DomainEventEmitter)
    const holidayUuid = generateHolidayUuid()

    const repository = createStubInstance(Repository<Holiday>)
    repository.findOne.resolves(null)

    const useCase = new UpdateHolidayUseCase(stubDataSource(), eventEmitter, repository)
    const command = new UpdateHolidayCommandBuilder().build()

    expect(useCase.execute(holidayUuid, command)).rejects.toThrow(HolidayNotFoundError)
  })

  it('throws HolidayNotInFutureError when existing holiday is not in the future', () => {
    const eventEmitter = createStubInstance(DomainEventEmitter)
    const holidayUuid = generateHolidayUuid()

    const existingHoliday = {
      uuid: holidayUuid,
      name: 'Past Holiday',
      startDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
      endDate: null,
      isRecurring: false,
      type: HolidayType.HOLIDAY,
      recurrence: null
    } as Holiday

    const repository = createStubInstance(Repository<Holiday>)
    repository.findOne.resolves(existingHoliday)

    const useCase = new UpdateHolidayUseCase(stubDataSource(), eventEmitter, repository)
    const command = new UpdateHolidayCommandBuilder()
      .withStartDate(dayjs().add(1, 'month').format('YYYY-MM-DD'))
      .build()

    expect(useCase.execute(holidayUuid, command)).rejects.toThrow(HolidayNotInFutureError)
  })

  it('throws InvalidHolidayStartDateError when new start date is in the past', () => {
    const eventEmitter = createStubInstance(DomainEventEmitter)
    const holidayUuid = generateHolidayUuid()

    const existingHoliday = {
      uuid: holidayUuid,
      name: 'Future Holiday',
      startDate: dayjs().add(1, 'month').format('YYYY-MM-DD'),
      endDate: null,
      isRecurring: false,
      type: HolidayType.HOLIDAY,
      recurrence: null
    } as Holiday

    const repository = createStubInstance(Repository<Holiday>)
    repository.findOne.resolves(existingHoliday)

    const useCase = new UpdateHolidayUseCase(stubDataSource(), eventEmitter, repository)
    const command = new UpdateHolidayCommandBuilder()
      .withStartDate(dayjs().subtract(1, 'day').format('YYYY-MM-DD'))
      .build()

    expect(useCase.execute(holidayUuid, command)).rejects.toThrow(InvalidHolidayStartDateError)
  })
})
