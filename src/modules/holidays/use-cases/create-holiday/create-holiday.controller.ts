import { Body, Controller, Post } from '@nestjs/common'
import { ApiCreatedResponse, ApiTags } from '@nestjs/swagger'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { ApiBadRequestErrorResponse } from '../../../../modules/exceptions/api-errors/api-error-response.decorator.js'
import { InvalidHolidayStartDateError } from '../../errors/invalid-holiday-start-date.error.js'
import { InvalidHolidayEndDateError } from '../../errors/invalid-holiday-end-date.error.js'
import { CreateHolidayUseCase } from './create-holiday.use-case.js'
import { CreateHolidayCommand } from './create-holiday.command.js'
import { CreateHolidayResponse } from './create-holiday.response.js'

@ApiTags('Holidays')
@Controller('holidays')
export class CreateHolidayController {
  constructor (
    private readonly useCase: CreateHolidayUseCase
  ) {}

  @Post()
  @Permissions(Permission.HOLIDAY_CREATE)
  @ApiBadRequestErrorResponse(
    InvalidHolidayStartDateError,
    InvalidHolidayEndDateError
  )
  @ApiCreatedResponse({ type: CreateHolidayResponse })
  async createHoliday (
    @Body() command: CreateHolidayCommand
  ): Promise<CreateHolidayResponse> {
    return await this.useCase.execute(command)
  }
}
