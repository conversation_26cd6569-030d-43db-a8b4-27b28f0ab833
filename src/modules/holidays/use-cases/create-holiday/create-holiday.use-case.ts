import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Repository } from 'typeorm'
import dayjs from 'dayjs'
import { DomainEventEmitter } from '../../../../modules/domain-events/domain-event-emitter.js'
import { Holiday } from '../../entities/holiday.entity.js'
import { InvalidHolidayStartDateError } from '../../errors/invalid-holiday-start-date.error.js'
import { InvalidHolidayEndDateError } from '../../errors/invalid-holiday-end-date.error.js'
import { CreateHolidayCommand } from './create-holiday.command.js'
import { HolidayCreatedEvent } from './holiday-created.event.js'
import { CreateHolidayResponse } from './create-holiday.response.js'

@Injectable()
export class CreateHolidayUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(Holiday)
    private readonly holidayRepository: Repository<Holiday>
  ) {}

  async execute (command: CreateHolidayCommand): Promise<CreateHolidayResponse> {
    this.validateStartInFuture(command.startDate)
    this.validateEndInFuture(command.endDate)

    const holiday = this.holidayRepository.create({
      name: command.name,
      startDate: command.startDate,
      endDate: command.endDate,
      isRecurring: command.isRecurring,
      type: command.holidayType,
      recurrence: command.recurrence
    })

    await transaction (this.dataSource, async () => {
      await this.holidayRepository.insert(holiday)
      await this.eventEmitter.emitOne(new HolidayCreatedEvent(holiday.uuid))
    })

    return new CreateHolidayResponse(holiday)
  }

  validateStartInFuture (start: string): void {
    const isStartValid = dayjs(start).isAfter(dayjs())

    if (!isStartValid) {
      throw new InvalidHolidayStartDateError(start)
    }
  }

  validateEndInFuture (end: string | null): void {
    if (end == null) {
      return
    }
    const isEndValid = dayjs(end).isAfter(dayjs())

    if (!isEndValid) {
      throw new InvalidHolidayEndDateError(end)
    }
  }
}
