import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Holiday } from '../../entities/holiday.entity.js'
import { DomainEventEmitterModule } from '../../../../modules/domain-events/domain-event-emitter.module.js'
import { CreateHolidayController } from './create-holiday.controller.js'
import { CreateHolidayUseCase } from './create-holiday.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Holiday
    ]),
    DomainEventEmitterModule
  ],
  controllers: [
    CreateHolidayController
  ],
  providers: [
    CreateHolidayUseCase
  ],
  exports: []
})
export class CreateHolidayModule {}
