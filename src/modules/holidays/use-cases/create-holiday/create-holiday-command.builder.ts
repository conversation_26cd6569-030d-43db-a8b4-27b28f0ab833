import { HolidayType } from '../../holiday-type.enum.js'
import { HolidayRecurrence } from '../../holiday-recurrence.enum.js'
import { CreateHolidayCommand } from './create-holiday.command.js'

export class CreateHolidayCommandBuilder {
  private command: CreateHolidayCommand

  constructor () {
    this.command = new CreateHolidayCommand()
    this.command.name = 'Kerstmis'
    this.command.holidayType = HolidayType.VACATION
    this.command.isRecurring = false
    this.command.startDate = new Date('2025-12-26').toISOString()
    this.command.endDate = null
    this.command.recurrence = HolidayRecurrence.ONE_TIME
  }

  withName (name: string): this {
    this.command.name = name
    return this
  }

  withHolidayType (holidayType: HolidayType): this {
    this.command.holidayType = holidayType
    return this
  }

  withIsRecurring (isRecurring: boolean): this {
    this.command.isRecurring = isRecurring
    return this
  }

  withStartDate (startDate: string): this {
    this.command.startDate = startDate
    return this
  }

  withEndDate (endDate: string | null): this {
    this.command.endDate = endDate
    return this
  }

  withRecurrence (recurrence: HolidayRecurrence): this {
    this.command.recurrence = recurrence
    return this
  }

  build (): CreateHolidayCommand {
    return this.command
  }
}
