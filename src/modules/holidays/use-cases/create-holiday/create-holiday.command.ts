import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsBoolean } from 'class-validator'
import { IsAfterDateString, IsDateWithoutTimeString, IsNullable, IsNullableWhen } from '@wisemen/validators'
import { HolidayType, HolidayTypeApiProperty } from '../../holiday-type.enum.js'
import { HolidayRecurrence, HolidayRecurrenceApiProperty } from '../../holiday-recurrence.enum.js'

export class CreateHolidayCommand {
  @ApiProperty({ type: String, description: 'The name of the holiday' })
  @IsString()
  @IsNotEmpty()
  name: string

  @HolidayTypeApiProperty()
  @IsNotEmpty()
  holidayType: HolidayType

  @ApiProperty({ type: Boolean, description: 'Indicates if the holiday is recurring' })
  @IsBoolean()
  @IsNotEmpty()
  isRecurring: boolean

  @ApiProperty({ type: String, format: 'date', description: 'The start date of the holiday, example: 2025-12-26' })
  @IsDateWithoutTimeString()
  startDate: string

  @ApiProperty({ type: String, format: 'date', nullable: true, description: 'The end date of the holiday, example: 2025-12-26' })
  @IsDateWithoutTimeString()
  @IsAfterDateString((obj: CreateHolidayCommand) => obj.startDate)
  @IsNullable()
  endDate: string | null

  @HolidayRecurrenceApiProperty()
  @IsNullableWhen((obj: CreateHolidayCommand) => obj.isRecurring === false)
  recurrence: HolidayRecurrence | null
}
