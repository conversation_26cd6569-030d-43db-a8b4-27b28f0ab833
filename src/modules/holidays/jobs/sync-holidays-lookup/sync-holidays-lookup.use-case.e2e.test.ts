import { after, before, describe, it } from 'node:test'
import { expect } from 'expect'
import { DataSource } from 'typeorm'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../test/setup/end-to-end-test-setup.js'
import { SyncHolidaysLookupUseCase } from './sync-holidays-lookup.use-case.js'

describe('sync holidays_lookup usecase e2e tests', () => {
  let testSetup: EndToEndTestSetup
  let dataSource: DataSource

  before(async () => {
    testSetup = await TestBench.setupEndToEndTest()
    dataSource = testSetup.dataSource
  })

  after(async () => {
    await testSetup.teardown()
  })

  it('executes the materialized holidays_lookup refresh', async () => {
    const useCase = new SyncHolidaysLookupUseCase(
      dataSource
    )

    await expect(useCase.execute())
      .resolves.not.toThrow()
  })
})
