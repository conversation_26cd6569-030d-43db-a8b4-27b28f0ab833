import { Injectable } from '@nestjs/common'
import { <PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JobHandler } from '@wisemen/pgboss-nestjs-job'
import { SyncHolidaysLookupJob } from './sync-holidays-lookup.job.js'
import { SyncHolidaysLookupUseCase } from './sync-holidays-lookup.use-case.js'

@Injectable()
@PgBossJobHandler(SyncHolidaysLookupJob)
export class SyncHolidaysLookupJobHandler extends JobHandler<SyncHolidaysLookupJob> {
  constructor (
    private readonly useCase: SyncHolidaysLookupUseCase
  ) {
    super()
  }

  async run (): Promise<void> {
    await this.useCase.execute()
  }
}
