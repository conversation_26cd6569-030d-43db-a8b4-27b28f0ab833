import { Injectable } from '@nestjs/common'
import { InjectDataSource } from '@wisemen/nestjs-typeorm'
import { DataSource } from 'typeorm'

@Injectable()
export class SyncHolidaysLookupUseCase {
  constructor (
    @InjectDataSource()
    private readonly datasource: DataSource
  ) {}

  async execute (): Promise<void> {
    await this.datasource.query(`REFRESH MATERIALIZED VIEW CONCURRENTLY holidays_lookup;`)
  }
}
