import { Column, CreateDate<PERSON>olumn, DeleteDate<PERSON><PERSON>umn, <PERSON>tity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'
import { HolidayRecurrence } from '../holiday-recurrence.enum.js'
import { HolidayType } from '../holiday-type.enum.js'
import { HolidayUuid } from './holiday.uuid.js'

@Entity()
export class Holiday {
  @PrimaryGeneratedColumn('uuid')
  uuid: HolidayUuid

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @DeleteDateColumn({ type: 'timestamptz' })
  deletedAt: Date | null

  @Column({ type: 'varchar' })
  name: string

  @Column({ type: 'date' })
  startDate: string

  @Column({ type: 'date', nullable: true })
  endDate: string | null

  @Column({ type: 'boolean', default: false })
  isRecurring: boolean

  @Column({ enum: HolidayType, type: 'enum', default: HolidayType.HOLIDAY })
  type: HolidayType

  @Column({ enum: HolidayRecurrence, type: 'enum', nullable: true })
  recurrence: HolidayRecurrence | null
}
