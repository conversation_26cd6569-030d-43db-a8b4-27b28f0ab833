import { Index, ViewColumn, ViewEntity } from 'typeorm'
import { HolidayType } from '../holiday-type.enum.js'

@ViewEntity({
  name: 'holidays_lookup',
  materialized: true,
  expression: `
    -- Non-recurring holidays
    SELECT
      uuid,
      name,
      daterange(
        start_date,
        COALESCE(end_date, start_date),
        '[]'
      ) AS date_range,
      EXTRACT(YEAR FROM start_date)::INT AS year,
      false AS is_recurring,
      type
    FROM holiday
    WHERE is_recurring = false

    UNION ALL

    -- Yearly recurring holidays
    SELECT
      h.uuid,
      h.name,
      daterange(
        make_date(y.year, EXTRACT(MONTH FROM start_date)::INT, EXTRACT(DAY FROM COALESCE(end_date, start_date))::INT),
        make_date(y.year, EXTRACT(MONTH FROM start_date)::INT, EXTRACT(DAY FROM COALESCE(end_date, start_date))::INT),
        '[]'
      ),
      y.year,
      true AS is_recurring,
      h.type
    FROM holiday h,
        generate_series(2020, 2140) AS y(year)
    WHERE h.is_recurring = true AND h.recurrence = 'yearly';

    SELECT
      daterange(make_date(
        2025,
        EXTRACT(
          MONTH
          FROM
            start_date
        )::INT,
        EXTRACT(
          DAY
          FROM
            COALESCE(end_date, start_date)
        )::INT
      ),
      make_date(
        2025,
        EXTRACT(
          MONTH
          FROM
            start_date
        )::INT,
        EXTRACT(
          DAY
          FROM
            COALESCE(end_date, start_date)
        )::INT
      ), '[]')
    FROM
      holiday h;
    `
})

@Index('holidays_lookup_unique_idx', ['uuid', 'year'], { unique: true })
export class HolidaysLookupView {
  @ViewColumn()
  uuid: string

  @ViewColumn()
  name: string

  @Index('holidays_lookup_daterange_idx', { spatial: true })
  @ViewColumn()
  dateRange: string

  @ViewColumn()
  year: string

  @ViewColumn()
  isRecurring: boolean

  @ViewColumn()
  type: HolidayType
}
