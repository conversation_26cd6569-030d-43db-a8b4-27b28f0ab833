import dayjs from 'dayjs'
import { HolidayType } from '../holiday-type.enum.js'
import { Holiday } from './holiday.entity.js'

export class HolidayEntityBuilder {
  private holiday: Holiday

  constructor () {
    this.holiday = new Holiday()

    this.holiday.createdAt = new Date()
    this.holiday.updatedAt = new Date()
    this.holiday.deletedAt = null
    this.holiday.name = ''
    this.holiday.startDate = dayjs().format('YYYY-MM-DD')
    this.holiday.endDate = null
    this.holiday.isRecurring = false
    this.holiday.type = HolidayType.HOLIDAY
    this.holiday.recurrence = null

    return this
  }

  withName (name: string): this {
    this.holiday.name = name
    return this
  }

  withStartDate (startDate: string): this {
    this.holiday.startDate = startDate
    return this
  }

  withEndDate (endDate: string | null): this {
    this.holiday.endDate = endDate
    return this
  }

  withIsRecurring (isRecurring: boolean): this {
    this.holiday.isRecurring = isRecurring
    return this
  }

  withType (type: HolidayType): this {
    this.holiday.type = type
    return this
  }

  build (): Holiday {
    return this.holiday
  }
}
