import { ApiErrorCode } from '../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { NotFoundApiError } from '../../../modules/exceptions/api-errors/not-found.api-error.js'

export class HolidayNotFoundError extends NotFoundApiError {
  @ApiErrorCode('holiday_not_found')
  code = 'holiday_not_found'

  meta: never

  constructor (holidayUuid: string) {
    super(`Holiday ${holidayUuid} not found`)
  }
}
