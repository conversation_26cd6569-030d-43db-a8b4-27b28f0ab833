import { ApiErrorCode } from '../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../../modules/exceptions/api-errors/bad-request.api-error.js'

export class InvalidHolidayEndDateError extends BadRequestApiError {
  @ApiErrorCode('invalid_holiday_end_date')
  code = 'invalid_holiday_end_date'

  meta: never

  constructor (date: string) {
    super(`Invalid holiday end date: ${date}. End date must be in the future.`)
  }
}
