import { ApiErrorCode } from '../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../../modules/exceptions/api-errors/bad-request.api-error.js'

export class InvalidHolidayStartDateError extends BadRequestApiError {
  @ApiErrorCode('invalid_holiday_start_date')
  code = 'invalid_holiday_start_date'

  meta: never

  constructor (date: string) {
    super(`Invalid holiday start date: ${date}. Start date must be in the future.`)
  }
}
