import { ApiErrorCode } from '../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../../modules/exceptions/api-errors/bad-request.api-error.js'

export class HolidayNotInFutureError extends BadRequestApiError {
  @ApiErrorCode('holiday_not_in_future')
  code = 'holiday_not_in_future'

  meta: never

  constructor (holidayUuid: string) {
    super(`Holiday ${holidayUuid} cannot be updated because it is not in the future`)
  }
}
