import { MigrationInterface, QueryRunner } from 'typeorm'

export class ReworkAddressColumn1749558540226 implements MigrationInterface {
  name = 'ReworkAddressColumn1749558540226'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "organization" DROP COLUMN "address_country"`)
    await queryRunner.query(`ALTER TABLE "organization" DROP COLUMN "address_city"`)
    await queryRunner.query(`ALTER TABLE "organization" DROP COLUMN "address_zip_code"`)
    await queryRunner.query(`ALTER TABLE "organization" DROP COLUMN "address_street"`)
    await queryRunner.query(`ALTER TABLE "organization" DROP COLUMN "address_street_number"`)
    await queryRunner.query(`ALTER TABLE "organization" DROP COLUMN "address_unit_number"`)
    await queryRunner.query(`ALTER TABLE "organization" DROP COLUMN "address_coordinates"`)
    await queryRunner.query(`ALTER TABLE "location" DROP COLUMN "address_country"`)
    await queryRunner.query(`ALTER TABLE "location" DROP COLUMN "address_city"`)
    await queryRunner.query(`ALTER TABLE "location" DROP COLUMN "address_zip_code"`)
    await queryRunner.query(`ALTER TABLE "location" DROP COLUMN "address_street"`)
    await queryRunner.query(`ALTER TABLE "location" DROP COLUMN "address_street_number"`)
    await queryRunner.query(`ALTER TABLE "location" DROP COLUMN "address_unit_number"`)
    await queryRunner.query(`ALTER TABLE "location" DROP COLUMN "address_coordinates"`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" DROP COLUMN "address_country"`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" DROP COLUMN "address_city"`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" DROP COLUMN "address_zip_code"`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" DROP COLUMN "address_street"`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" DROP COLUMN "address_street_number"`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" DROP COLUMN "address_unit_number"`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" DROP COLUMN "address_coordinates"`)
    await queryRunner.query(`ALTER TABLE "care_user" DROP COLUMN "address_country"`)
    await queryRunner.query(`ALTER TABLE "care_user" DROP COLUMN "address_city"`)
    await queryRunner.query(`ALTER TABLE "care_user" DROP COLUMN "address_zip_code"`)
    await queryRunner.query(`ALTER TABLE "care_user" DROP COLUMN "address_street"`)
    await queryRunner.query(`ALTER TABLE "care_user" DROP COLUMN "address_street_number"`)
    await queryRunner.query(`ALTER TABLE "care_user" DROP COLUMN "address_unit_number"`)
    await queryRunner.query(`ALTER TABLE "care_user" DROP COLUMN "address_coordinates"`)
    await queryRunner.query(`ALTER TABLE "organization" ADD "address" jsonb NOT NULL`)
    await queryRunner.query(`ALTER TABLE "location" ADD "address" jsonb NOT NULL`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" ADD "address" jsonb NOT NULL`)
    await queryRunner.query(`ALTER TABLE "care_user" ADD "address" jsonb`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "care_user" DROP COLUMN "address"`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" DROP COLUMN "address"`)
    await queryRunner.query(`ALTER TABLE "location" DROP COLUMN "address"`)
    await queryRunner.query(`ALTER TABLE "organization" DROP COLUMN "address"`)
    await queryRunner.query(`ALTER TABLE "care_user" ADD "address_coordinates" geometry(POINT,4326) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "care_user" ADD "address_unit_number" character varying`)
    await queryRunner.query(`ALTER TABLE "care_user" ADD "address_street_number" character varying`)
    await queryRunner.query(`ALTER TABLE "care_user" ADD "address_street" character varying`)
    await queryRunner.query(`ALTER TABLE "care_user" ADD "address_zip_code" character varying`)
    await queryRunner.query(`ALTER TABLE "care_user" ADD "address_city" character varying`)
    await queryRunner.query(`ALTER TABLE "care_user" ADD "address_country" character varying`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" ADD "address_coordinates" geometry(POINT,4326) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" ADD "address_unit_number" character varying`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" ADD "address_street_number" character varying`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" ADD "address_street" character varying`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" ADD "address_zip_code" character varying`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" ADD "address_city" character varying`)
    await queryRunner.query(`ALTER TABLE "invoice_detail" ADD "address_country" character varying`)
    await queryRunner.query(`ALTER TABLE "location" ADD "address_coordinates" geometry(POINT,4326) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "location" ADD "address_unit_number" character varying`)
    await queryRunner.query(`ALTER TABLE "location" ADD "address_street_number" character varying`)
    await queryRunner.query(`ALTER TABLE "location" ADD "address_street" character varying`)
    await queryRunner.query(`ALTER TABLE "location" ADD "address_zip_code" character varying`)
    await queryRunner.query(`ALTER TABLE "location" ADD "address_city" character varying`)
    await queryRunner.query(`ALTER TABLE "location" ADD "address_country" character varying`)
    await queryRunner.query(`ALTER TABLE "organization" ADD "address_coordinates" geometry(POINT,4326) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "organization" ADD "address_unit_number" character varying`)
    await queryRunner.query(`ALTER TABLE "organization" ADD "address_street_number" character varying`)
    await queryRunner.query(`ALTER TABLE "organization" ADD "address_street" character varying`)
    await queryRunner.query(`ALTER TABLE "organization" ADD "address_zip_code" character varying`)
    await queryRunner.query(`ALTER TABLE "organization" ADD "address_city" character varying`)
    await queryRunner.query(`ALTER TABLE "organization" ADD "address_country" character varying`)
  }
}
